import React from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useAppDispatch } from "@src/redux/store";
import { WildcardNewOpportunity } from "@src/components/WildCard";

export default function NewOpportunityPage({ subdomain, pageDetail }: any) {
  const dispatch = useAppDispatch();

  return (
    <>
      <section className="opportunity">
        <WildcardNewOpportunity
          subdomain={subdomain}
          pageTitle={pageDetail?.title ?? "Opportunity Management"}
          breadcrumb={[`New ${pageDetail?.singular_name ?? "Opportunity"}`]}
          pageDescription={`Add New ${pageDetail?.singular_name ?? "Opportunity"}`}
        />
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({ req }) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    {
      wildcard: true,
    },
    "opportunities",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read", "write"],
      pageDetail: pageDetail,
    },
  };
};

NewOpportunityPage.layout = PrivateLayout;
