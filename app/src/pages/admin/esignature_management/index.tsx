import React, { useCallback, useEffect, useState } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import { getAllJobRequestList, openDialog, setLoader, closeDialog } from "@src/redux/actions";
import { useEmployeePagePermissions } from "@src/helper/pagePermissions";
import {
    KeyPairInterface,
    PagePermissionInterface,
} from "@src/redux/interfaces";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { APP_ROUTE } from "@src/constants";
import { useRouter } from "next/router";
import { WildcardEsignatureList } from "@src/components/WildCard/ESignature/WildcardEsignatureList";
import { But<PERSON> } from "antd";
import Link from "next/link";
import { eSignatureApi } from "@src/apis/wildcardApis";
import { Dialog } from "@src/components/Dialog/Dialog";
import DialogComponents from "@src/components/DialogComponents";
import flashMessage from "@src/components/FlashMessage";


interface SearchParams {
    page: number;
    search?: string;
}

type ESignatureManagementPageProps = {
    subdomain: string;
    allowedRoles?: string[];
    pagePermissions?: PagePermissionInterface;
    requiredPermission?: string[];
    filters: SearchParams;
    pageDetail: KeyPairInterface;
};


export default function ESignatureManagementPage({
    subdomain,
    pagePermissions,
    filters,
    pageDetail,
}: ESignatureManagementPageProps) {
    const limit = 10;
    const [currentPage, setCurrentPage] = useState(1);
    const [rows, setRows] = useState<any[]>([]);
    const [count, setCount] = useState(0);

    const dispatch = useAppDispatch();
    const router = useRouter();

    const currentPagePermissions: string[] = useEmployeePagePermissions({
        pagePermissions,
    });

    const fetchData = useCallback(
        async (currentPage: number, limit: number) => {
            await dispatch(setLoader(true));
            setCurrentPage(currentPage);
            const {success, ...data} = await eSignatureApi.getEmployeeSignatures({
                    page: currentPage,
                    limit: limit
            })
            setRows(data.data.rows);
            setCount(data.data.count);
            await dispatch(setLoader(false));
        },
        [dispatch],
    );

    useEffect(() => {
        if (subdomain) {
            fetchData(filters?.page ?? 1, limit ?? 10);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [subdomain]);

    useEffect(() => {
        let queryParams: SearchParams = { page: currentPage };
        router.push(
            { pathname: APP_ROUTE.ESIGNATURE_MANAGEMENT, query: queryParams as any },
            undefined,
            { shallow: true },
        );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentPage]);

    const AddSignature = async (formData: any) =>{
        console.log("formData", formData);
        

        await dispatch(setLoader(true));
        const {success, ...data} = await eSignatureApi.createEmployeeSignature(formData);
        await dispatch(setLoader(false));
        if (success) {
            flashMessage(data.message, "success");
            dispatch(closeDialog());
            fetchData(currentPage, limit);
        } else {
            flashMessage(data.message, "error");
        }
    }

    const HandleAddSignatureModal = () => {
        dispatch(
            openDialog({
                config: DialogComponents.EMPLOYEE_SIGNATURE_MODAL,
                options: {
                  title: "Add New Signature",
                  onAddSignature: (fromData: any,
                  ) => AddSignature(fromData),
                },
              }),
            );
    }

    const addNewButton = currentPagePermissions.includes("write") ? (
        <Button className="btn btn-theme ms-auto" onClick={HandleAddSignatureModal}>
            + Add Signature
        </Button>
    ) : null;


    return (
        <>
            <section className="">
                <PageHeader
                    pageTitle={pageDetail?.title ?? "E-Signature Management"}
                    pageDescription={
                        pageDetail?.description ?? "E-Signature Management Description"
                    }
                    buttonComponent={rows.length >= 0 && addNewButton}
                    >
                        
                    <div className="box-content">
                        <WildcardEsignatureList
                            rows={rows}
                            limit={limit}
                            count={count}
                            currentPage={currentPage}
                            fetchData={fetchData}
                            subdomain={subdomain}
                        />
                    </div>
                </PageHeader>
            </section>

        </>
    )
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
    req,
    res,
    query,
}) => {
    const {
        redirectUrl,
        validate,
        subdomain,
        allowedRoles,
        pagePermissions,
        pageDetail,
    } = await extractSubdomainAndDomain(req, { wildcard: true }, "job_requests");
    if (!validate) {
        return {
            redirect: {
                destination: redirectUrl,
                permanent: false,
            },
        };
    }

    let filters: SearchParams = {
        page: 1,
    };

    if (query) {
        const { search } = query;
        const page = query.page ? Number(query.page) : 1;
        // `search` should be a string or undefined
        const searchString = typeof search === "string" ? search : undefined;

        // Construct filters object with default values or undefined
        filters = {
            ...filters, // Spread the existing properties (if any)
            ...(searchString ? { search: searchString } : {}), // Conditionally add `search`
            ...(page && Number.isNaN(page) ? {} : { page }), // Conditionally add `page` if it's valid
        };
    }

    return {
        props: {
            subdomain: subdomain,
            allowedRoles: allowedRoles,
            pagePermissions: pagePermissions,
            requiredPermission: ["read"],
            filters: filters,
            pageDetail: pageDetail,
        },
    };
};

ESignatureManagementPage.layout = PrivateLayout;
