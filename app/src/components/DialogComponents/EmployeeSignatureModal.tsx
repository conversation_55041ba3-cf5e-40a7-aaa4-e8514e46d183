import React, { useState } from "react";
import { Button, Upload, message } from "antd";
import ArchiveIcon from "@mui/icons-material/Archive";
import type { UploadProps } from "antd";
import flashMessage from "../FlashMessage";

// Props for the EmployeeSignatureModal component
type EmployeeSignatureModalProps = {
  onSignatureUpload?: (signatureFile: File) => void;
  close: () => void;
  employee?: any;
};

// Component for the modal to upload employee signature
const EmployeeSignatureModal = ({
  close,
  onSignatureUpload,
  employee,
}: EmployeeSignatureModalProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [uploading, setUploading] = useState<boolean>(false);

  // Handle file selection
  const handleFileSelect = (file: File) => {
    // Validate file type
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      message.error('Please select a valid image file (PNG, JPG, JPEG, GIF)');
      return false;
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      message.error('File size must be less than 5MB');
      return false;
    }

    setSelectedFile(file);

    // Create preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    return false; // Prevent auto upload
  };

  // Handle upload submission
  const handleUpload = async () => {
    if (!selectedFile) {
      message.error('Please select a signature file first');
      return;
    }

    setUploading(true);

    try {
      // Simulate upload process for local system
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (onSignatureUpload) {
        onSignatureUpload(selectedFile);
      }

      flashMessage('Signature uploaded successfully!', 'success');
      close();
    } catch (error) {
      message.error('Failed to upload signature');
    } finally {
      setUploading(false);
    }
  };

  const uploadProps: UploadProps = {
    beforeUpload: handleFileSelect,
    showUploadList: false,
    accept: 'image/*',
  };

  return (
    <div className="row">
      <div className="col-md-12">
        <div className="card card-border">
          <div className="card-body">
            <h5 className="mb-4">Upload Signature</h5>

            {employee && (
              <div className="mb-3">
                <strong>Employee:</strong> {employee.name || employee.first_name + ' ' + employee.last_name}
              </div>
            )}

            <div className="mb-4">
              <label className="form-label">Select Signature File *</label>
              <Upload {...uploadProps}>
                <Button icon={<ArchiveIcon />} size="large" block>
                  Click to Select Signature File
                </Button>
              </Upload>
              <small className="text-muted mt-2 d-block">
                Supported formats: PNG, JPG, JPEG, GIF (Max size: 5MB)
              </small>
            </div>

            {selectedFile && (
              <div className="mb-4">
                <div className="alert alert-info">
                  <strong>Selected File:</strong> {selectedFile.name} ({(selectedFile.size / 1024).toFixed(2)} KB)
                </div>
              </div>
            )}

            {previewUrl && (
              <div className="mb-4">
                <label className="form-label">Preview:</label>
                <div className="text-center p-3 border rounded">
                  <img
                    src={previewUrl}
                    alt="Signature Preview"
                    style={{
                      maxWidth: "100%",
                      maxHeight: "200px",
                      border: "1px solid #ddd",
                      borderRadius: "4px"
                    }}
                  />
                </div>
              </div>
            )}

            <div className="d-flex gap-2 justify-content-end">
              <Button onClick={close}>
                Cancel
              </Button>
              <Button
                type="primary"
                onClick={handleUpload}
                loading={uploading}
                disabled={!selectedFile}>
                Upload Signature
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeSignatureModal;
