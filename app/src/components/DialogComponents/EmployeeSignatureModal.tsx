import React, { useState, useRef, useEffect } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { EmployeeInterface, KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import flashMessage from "../FlashMessage";
import { employeeManagementApi } from "@src/apis/wildcardApis";
import SignatureCanvas from "react-signature-canvas";
import { Button } from "antd";

// Default values for the employee signature form
const defaultValue: KeyPairInterface = {
  employee_id: "",
  signature: "",
  status: 1,
};

// Props for the EmployeeSignatureModal component
type EmployeeSignatureModalProps = {
  onSignatureCreate?: (signature: any) => void;
  close: () => void;
  employee?: any;
  existingSignature?: any;
  mode?: "create" | "edit";
};

// Component for the modal to create/edit employee signature
const EmployeeSignatureModal = ({
  close,
  onSignatureCreate,
  employee,
  existingSignature,
  mode = "create",
}: EmployeeSignatureModalProps) => {
  const dispatch = useAppDispatch();
  const sigCanvas: any = useRef(null);

  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>({
    ...defaultValue,
    employee_id: employee?.id || existingSignature?.employee_id || "",
    signature: existingSignature?.signature || "",
    status: existingSignature?.status || 1,
  });

  const [employees, setEmployees] = useState<Array<{ value: number; label: string }>>([]);
  const [signatureError, setSignatureError] = useState<string>("");

  // Fields for the employee signature form
  const EmployeeSignatureFields: GlobalInputFieldType[] = [
    {
      name: "employee_id",
      label: "Employee",
      type: "select",
      dataType: "select",
      options: employees,
      placeholder: "Select Employee",
      required: true,
      disabled: !!employee, // Disable if employee is pre-selected
    },
    {
      name: "status",
      label: "Status",
      type: "select",
      dataType: "select",
      options: [
        { value: 1, label: "Active" },
        { value: 0, label: "Inactive" },
      ],
      placeholder: "Select Status",
      required: true,
    },
  ];

  // Fetch employees list for dropdown
  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        const { success, data } = await employeeManagementApi.getEmployeesOptions({});
        if (success) {
          setEmployees(data || []);
        }
      } catch (error) {
        console.error("Error fetching employees:", error);
      }
    };

    if (!employee) {
      fetchEmployees();
    }
  }, [employee]);

  // Load existing signature if in edit mode
  useEffect(() => {
    if (mode === "edit" && existingSignature?.signature && sigCanvas.current) {
      // Create an image and load the signature
      const img = new Image();
      img.onload = () => {
        const canvas = sigCanvas.current.getCanvas();
        const ctx = canvas.getContext("2d");
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0);
      };
      img.src = existingSignature.signature;
    }
  }, [mode, existingSignature]);

  const handleSignatureClear = () => {
    if (sigCanvas.current) {
      sigCanvas.current.clear();
    }
    setState((prevState: any) => ({
      ...prevState,
      signature: "",
    }));
    setSignatureError("");
  };

  const handleSignatureSave = () => {
    if (sigCanvas.current.isEmpty()) {
      setSignatureError("Please provide a signature");
      return;
    }

    const originalCanvas = sigCanvas.current.getCanvas();
    const scaleFactor = 0.5; // Scale down to 50%

    const resizedWidth = originalCanvas.width * scaleFactor;
    const resizedHeight = originalCanvas.height * scaleFactor;

    // Create off-screen canvas
    const resizedCanvas = document.createElement("canvas");
    resizedCanvas.width = resizedWidth;
    resizedCanvas.height = resizedHeight;

    const ctx = resizedCanvas.getContext("2d");
    if (ctx) {
      ctx.scale(scaleFactor, scaleFactor);
      ctx.drawImage(originalCanvas, 0, 0);
    }

    const data = resizedCanvas.toDataURL("image/png");

    setState((prevState: any) => ({
      ...prevState,
      signature: data,
    }));
    setSignatureError("");
  };

  // Function to handle form submission
  const onSubmit = async () => {
    // Validate signature
    if (!state.signature) {
      setSignatureError("Please provide a signature");
      return;
    }

    await dispatch(setLoader(true));
    
    try {
      let response;
      if (mode === "create") {
        // Call API to create employee signature
        response = await employeeManagementApi.createEmployeeSignature(state);
      }

      const { success, ...responseData } = response;
      await dispatch(setLoader(false));
      
      // Display flash message based on API response
      flashMessage(responseData.message, success ? "success" : "error");
      
      // If signature operation is successful, close the modal and call callback
      if (success) {
        close();
        if (mode === "create" && onSignatureCreate) {
          onSignatureCreate(responseData.data);
        }
      }
    } catch (error) {
      await dispatch(setLoader(false));
      flashMessage("An error occurred while saving the signature", "error");
    }
  };

  return (
    <>
      <div className="row">
        <div className="col-md-8 col-sm-12">
          <div className="card card-border mb-4">
            <div className="card-body">
              <ModalFormInput
                buttonTitle={mode === "create" ? "Create Signature" : "Update Signature"}
                fields={EmployeeSignatureFields}
                setState={setState}
                state={state}
                onSubmit={() => {}} // We'll handle submission with custom button
                onClose={close}
                customButtons={<></>} // Hide default buttons, we'll use custom ones
              />
            </div>
          </div>
          
          <div className="card card-border mb-0">
            <div className="card-body">
              <div>
                <label className="form-label">Signature *</label>
              </div>
              <div className="signature-box">
                <div className="signature-box-inner">
                  
                </div>
                <div className="signature-box-btn mt-2">
                  <Button
                    className="me-2"
                    onClick={handleSignatureClear}>
                    Clear
                  </Button>
                  <Button
                    type="primary"
                    onClick={handleSignatureSave}>
                    Save Signature
                  </Button>
                </div>
                {signatureError && (
                  <div className="text-danger mt-2">{signatureError}</div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <div className="col-md-4 col-sm-12">
          <div className="card card-border">
            <div className="card-body">
              <h6 className="mb-3">Signature Preview</h6>
              {state.signature ? (
                <div className="text-center">
                  <img 
                    src={state.signature} 
                    alt="Signature Preview" 
                    style={{ maxWidth: "100%", border: "1px solid #ccc" }}
                  />
                </div>
              ) : (
                <div className="text-center text-muted">
                  No signature saved yet
                </div>
              )}
              
              <div className="mt-4">
                <Button
                  type="primary"
                  block
                  onClick={onSubmit}
                  disabled={!state.signature}>
                  {mode === "create" ? "Create Signature" : "Update Signature"}
                </Button>
                <Button
                  block
                  className="mt-2"
                  onClick={close}>
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default EmployeeSignatureModal;
