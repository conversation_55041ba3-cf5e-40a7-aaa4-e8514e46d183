import React, { useEffect, useState } from "react";
import { Button, Upload } from "antd";
import ArchiveIcon from "@mui/icons-material/Archive";
import { FormGroup, FormLabel } from "react-bootstrap";
import flashMessage from "../FlashMessage";
import SelectField from "@src/components/input/SelectField";
import { useAppDispatch } from "@src/redux/store";
import { getAllEmployeeOptions } from "@src/redux/actions";
import { eSignatureApi } from "@src/apis/wildcardApis";

// Props for the EmployeeSignatureModal component
type EmployeeSignatureModalProps = {
  onAddSignature: (signatureData: any) => void;
  close: () => void;
};

// Component for the modal to upload employee signature
const EmployeeSignatureModal = ({
  close,
  onAddSignature
}: EmployeeSignatureModalProps) => {
  const [uploadedSignature, setUploadedSignature] = useState<Array<any>>([]);
  const [employee, setEmployee] = useState<any>(null);
  const [allEmployees, setAllEmployees] = useState<Array<any>>([]);
  const [uploading, setUploading] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  

  const dispatch = useAppDispatch();

  useEffect(()=>{
    fetchEmployees("");
  },[])

  const fetchEmployees = async (search:string) => {
    await setLoading(true);
    const { success, ...response } = await eSignatureApi.getActiveEmployees({ search: search });
    if (success) {
      setAllEmployees(response.data);
    }
    await setLoading(false);
  };


  // Function to validate the uploaded file before submission
  const beforeUpload = async (file: File) => {
    const fileExtension = file.name.split(".").pop()?.toLowerCase();
    const allowedFileTypes = ["png", "jpg", "jpeg", "gif"];

    if (!fileExtension || !allowedFileTypes.includes(fileExtension)) {
      flashMessage(
        `Only ${allowedFileTypes.join(", ")} files are accepted.`,
        "error",
      );
      return Upload.LIST_IGNORE;
    }

    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      flashMessage("Maximum file upload size allowed is 5 MB.", "error");
      return Upload.LIST_IGNORE;
    }

    const isFileExist = uploadedSignature.some(
      (uploadedFile) =>
        uploadedFile.name === file.name && uploadedFile.size === file.size,
    );

    if (isFileExist) {
      flashMessage(`${file.name} has already been uploaded.`, "error");
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  // Function to handle file changes
  const handleChange = async (info: any) => {
    const { status } = info.file;
    if (!status || status === "error") {
      return null;
    }
    const files = info.fileList;
    if (files) {
      setUploadedSignature(files);
    }
  };

  // Function to upload the signature file
  const uploadSignature = async () => {
    if (
      !uploadedSignature ||
      (uploadedSignature !== undefined &&
        uploadedSignature !== null &&
        Object.keys(uploadedSignature).length === 0) ||
      uploadedSignature === undefined ||
      uploadedSignature === null
    ) {
      flashMessage("Please upload the signature file.", "error");
      return;
    }

    if (!employee) {
      flashMessage("Please select an employee.", "error");
      return;
    }

    setUploading(true);

    try {
      console.log("Uploading signature for employee:", employee);
      if (uploadedSignature.length > 0) {
        const formData = new FormData();
        formData.append(`file`, uploadedSignature[0].originFileObj);
        formData.append(`employee_id`, employee.toString());
        formData.append(`status`, '1');

        // Call the API to create the signature
        const { success, message, ...response } = await eSignatureApi.createEmployeeSignature(formData);

        if (success) {
          flashMessage(message, "success");
          onAddSignature(response.data);
          close();
        } else {
          flashMessage(message || "Failed to upload signature", "error");
        }
      }
    } catch (error) {
      flashMessage('Failed to upload signature', 'error');
    } finally {
      setUploading(false);
    }
  };

  const onEmployeeChange = (event: any) => {
    setEmployee(event.target.value);
  };

  const handleRemove = (uid: string) => {
    setUploadedSignature(uploadedSignature.filter((file) => file.uid !== uid));
  };

  
  return (
    <>
      <FormGroup className="mb-3">
        <SelectField
          value={employee}
          name={"oppportunity_id"}
          label={"Job"}
          placeholder={"Select Job"}
          showSearch={true}
          onClear={() => setEmployee(null)}
          options={allEmployees}
          onSearch={fetchEmployees}
          loading={loading}
          selectEmpty
          onChangeInput={onEmployeeChange}
        />
        <div className="mb-3 group-relative">
          <FormLabel style={{ color: "black" }}>
            Upload Signature<span style={{ color: "red" }}>*</span>
          </FormLabel>
          <Upload.Dragger
            name="file"
            multiple={false}
            beforeUpload={beforeUpload}
            onChange={handleChange}
            onRemove={() => setUploadedSignature([])}
            accept=".png,.jpg,.jpeg,.gif"
            maxCount={1}
            showUploadList={false}
            fileList={uploadedSignature}>
            <p className="ant-upload-drag-icon">
              <ArchiveIcon fontSize="large" color="primary" />
            </p>
            <p className="ant-upload-text">
              Click or drag file to this area to upload
            </p>
            <p className="ant-upload-text">
              Maximum file upload size allowed is 5 MB.
            </p>
          </Upload.Dragger>
        </div>

        {uploadedSignature && uploadedSignature.length > 0 && (
          <div className="ant-upload-list ant-upload-list-text">
            <div className="ant-upload-list-item-container">
              {uploadedSignature.map((item, index) => {
                return (
                  <div
                    className="ant-upload-list-item ant-upload-list-item-done"
                    key={index}>
                    <span
                      role="img"
                      aria-label="paper-clip"
                      className="anticon anticon-paper-clip mr-1">
                      <svg
                        viewBox="64 64 896 896"
                        focusable="false"
                        data-icon="paper-clip"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                        aria-hidden="true">
                        <path d="M779.3 196.6c-94.2-94.2-247.6-94.2-341.7 0l-261 260.8c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l261-260.8c32.4-32.4 75.5-50.2 121.3-50.2s88.9 17.8 121.2 50.2c32.4 32.4 50.2 75.5 50.2 121.2 0 45.8-17.8 88.8-50.2 121.2l-266 265.9-43.1 43.1c-40.3 40.3-105.8 40.3-146.1 0-19.5-19.5-30.2-45.4-30.2-73s10.7-53.5 30.2-73l263.9-263.8c6.7-6.6 15.5-10.3 24.9-10.3h.1c9.4 0 18.1 3.7 24.7 10.3 6.7 6.7 10.3 15.5 10.3 24.9 0 9.3-3.7 18.1-10.3 24.7L372.4 653c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l215.6-215.6c19.9-19.9 30.8-46.3 30.8-74.4s-11-54.6-30.8-74.4c-41.1-41.1-107.9-41-149 0L463 364 224.8 602.1A172.22 172.22 0 00174 724.8c0 46.3 18.1 89.8 50.8 122.5 33.9 33.8 78.3 50.7 122.7 50.7 44.4 0 88.8-16.9 122.6-50.7l309.2-309C824.8 492.7 850 432 850 367.5c.1-64.6-25.1-125.3-70.7-170.9z"></path>
                      </svg>
                    </span>
                    <span
                      className="ant-upload-list-item-name"
                      title={item.name}>
                      {item.name}
                    </span>
                    <span className="ant-upload-list-item-actions">
                      <Button
                        title="Remove file"
                        onClick={() => handleRemove(item.uid)}
                        className="ant-btn css-dev-only-do-not-override-n0gjrg ant-btn-text ant-btn-sm ant-btn-icon-only ant-upload-list-item-action no-background-button">
                        <span className="ant-btn-icon">
                          <span
                            role="img"
                            aria-label="delete"
                            tabIndex={-1}
                            className="anticon anticon-delete">
                            <svg
                              viewBox="64 64 896 896"
                              focusable="false"
                              data-icon="delete"
                              width="1em"
                              height="1em"
                              fill="currentColor"
                              aria-hidden="true">
                              <path d="M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"></path>
                            </svg>
                          </span>
                        </span>
                      </Button>
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </FormGroup>

      <div className="ant-modal-footer mt-2 mb-1">
        <Button
          key="upload"
          className="btn btn-theme font-14 mr-1"
          onClick={uploadSignature}
          loading={uploading}>
          Upload
        </Button>
        <Button className="btn" key="cancel" onClick={() => close()}>
          Cancel
        </Button>
      </div>
    </>
  );
};

export default EmployeeSignatureModal;
