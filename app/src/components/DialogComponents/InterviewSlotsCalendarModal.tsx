import React, { useEffect, useState } from "react";
import { Calendar, momentLocalizer, Views } from "react-big-calendar";
import moment from "moment";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { Button, Modal, Form, Row, Col } from "react-bootstrap";
import { interviewApi } from "@src/apis/wildcardApis";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import flashMessage from "../FlashMessage";

const localizer = momentLocalizer(moment);

export interface InterviewEvent {
  id: number;
  title: string;
  start: Date;
  end: Date;
  interviewer: string;
  candidate: string;
  status: string;
  isUserCreated?: boolean; // Flag to identify user-created slots
}

interface InterviewSlotsCalendarModalProps {
  onConfirm: (
    selectedDate: string,
    duration: number,
    scheduleOriginalTime: any,
    slotEvent: InterviewEvent,
  ) => void;
  interviewerId: number;
  scheduleOriginalTime: any;
  userSlot: InterviewEvent | null;
  setUserSlot: (slot: InterviewEvent | null) => void;
  onClose: () => void;
}

const InterviewSlotsCalendarModal: React.FC<
  InterviewSlotsCalendarModalProps
> = ({
  onConfirm,
  interviewerId,
  scheduleOriginalTime,
  userSlot,
  setUserSlot,
}) => {
  const [events, setEvents] = useState<InterviewEvent[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [userEventId, setUserEventId] = useState<number | null>(null);
  const [errors, setErrors] = useState<{ start?: string; end?: string }>({});
  const [viewSlot, setViewSlot] = useState<InterviewEvent | any>(null);
  const [editSlot, setEditSlot] = useState<InterviewEvent | null>(null);
  const [userSlotData, setUserSlotData] = useState<InterviewEvent | null>(
    userSlot,
  );
  const [calendarRange, setCalendarRange] = useState<{
    start: Date;
    end: Date;
  } | null>(null);
  const [timeSlot, setTimeSlot] = useState<{ startTime: any; endTime: any }>(
    () => {
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();

      const startTime =
        scheduleOriginalTime?.start ||
        `${String(currentHour).padStart(2, "0")}:${String(currentMinute).padStart(2, "0")}`;
      const endHour = (currentHour + 1) % 24;
      const endTime =
        scheduleOriginalTime?.end ||
        `${String(endHour).padStart(2, "0")}:${String(currentMinute).padStart(2, "0")}`;

      return { startTime, endTime };
    },
  );

  const dispatch = useAppDispatch();

  useEffect(() => {
    // Default to week view, but you can use your defaultView if different
    const now = new Date();
    const startOfWeek = moment(now).startOf("week").toDate();
    const endOfWeek = moment(now).endOf("week").toDate();
    const formattedStart: any = moment(startOfWeek).format(
      "YYYY-MM-DDTHH:mm:ss",
    );
    const formattedEnd: any = moment(endOfWeek).format("YYYY-MM-DDTHH:mm:ss");
    setCalendarRange({ start: formattedStart, end: formattedEnd });
    // Optionally, log or use this range as needed
  }, []);

  useEffect(() => {
    if (calendarRange?.start && calendarRange?.end) {
      fetchInterviewSlots();
    }
  }, [calendarRange]);

  useEffect(() => {
    setEvents((prev) => {
      // Remove any previous user-created slot and add the current one
      const filtered = prev.filter((ev) => !ev.isUserCreated);
      return userSlot ? [...filtered, userSlot] : filtered;
    });
    setUserSlotData(userSlot);
  }, [userSlot]);

  const fetchInterviewSlots = async () => {
    dispatch(setLoader(true));
    try {
      // Fetch backend interview slots
      const { success, data } = await interviewApi.getInterviewSlots(
        interviewerId,
        { start_date: calendarRange?.start, end_date: calendarRange?.end },
      );
      let backendEvents: InterviewEvent[] = [];

      if (success && data.length) {
        backendEvents = data.map((interview: any) => {
          const startTime = new Date(`${interview.interview_at}.000Z`);
          const endTime = new Date(startTime);
          endTime.setMinutes(
            endTime.getMinutes() + interview?.interview_duration,
          );

          return {
            id: interview.id,
            title: interview.opportunity_title || "Interview",
            start: startTime, // already local
            end: endTime,
            interviewer: interview.interviewer_name || "Not assigned",
            candidate: interview.candidate_name || "Not assigned",
            status: interview.status_name || "Scheduled",
            isUserCreated: false,
          };
        });
      }
      if (userSlot) {
        setEvents([...backendEvents, userSlot]);
      } else {
        setEvents(backendEvents);
      }
    } catch (error) {
      console.error("Error fetching interview slots:", error);
    } finally {
      dispatch(setLoader(false));
    }
  };

  const handleSelectSlot = ({ start }: { start: Date }) => {
    const now = new Date();
    // Remove time part for date-only comparison
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const selected = new Date(
      start.getFullYear(),
      start.getMonth(),
      start.getDate(),
    );

    if (selected < today) {
      flashMessage("You cannot select a previous date.", "warning");
      return;
    }
    if (userSlotData) {
      flashMessage(
        "You can only add one slot. Please edit the existing one.",
        "info",
      );
      return;
    }

    setSelectedDate(start);
    const hours = start.getHours();
    const minutes = start.getMinutes();
    const roundedMinutes = Math.round(minutes / 15) * 15;
    const startTime = `${String(hours).padStart(2, "0")}:${String(roundedMinutes).padStart(2, "0")}`;
    const endHour = (hours + 1) % 24;
    const endTime = `${String(endHour).padStart(2, "0")}:${String(roundedMinutes).padStart(2, "0")}`;

    setTimeSlot({ startTime, endTime });
    if (!viewSlot) {
      setShowModal(true);
    }
  };

  const handleSelectEvent = (event: InterviewEvent) => {
    if (event?.isUserCreated == true) {
      setEditSlot(event);
      setTimeSlot({
        startTime: `${String(event.start.getHours()).padStart(2, "0")}:${String(event.start.getMinutes()).padStart(2, "0")}`,
        endTime: `${String(event.end.getHours()).padStart(2, "0")}:${String(event.end.getMinutes()).padStart(2, "0")}`,
      });
      setSelectedDate(event.start);
      setShowModal(true);
      return;
    }
    if (event.isUserCreated == false) {
      setViewSlot(event);
      setShowModal(true);
      return;
    }
    // Only allow editing user-created slots
    if (!event.isUserCreated) {
      flashMessage("You can only edit slots that you have created.", "info");
      return;
    }

    if (event.id !== userEventId) {
      flashMessage("You can only edit your most recently added slot.", "info");
      return;
    }

    setSelectedDate(event.start);
    const startTime = `${String(event.start.getHours()).padStart(2, "0")}:${String(event.start.getMinutes()).padStart(2, "0")}`;
    const endTime = `${String(event.end.getHours()).padStart(2, "0")}:${String(event.end.getMinutes()).padStart(2, "0")}`;
    setTimeSlot({ startTime, endTime });
    if (!viewSlot) {
      setShowModal(true);
    }
  };

  const handleSaveTimeSlot = () => {
    if (!selectedDate || !timeSlot.startTime || !timeSlot.endTime) return;

    const newErrors: { start?: string; end?: string } = {};

    const [startHour, startMinute] = timeSlot.startTime.split(":").map(Number);
    const [endHour, endMinute] = timeSlot.endTime.split(":").map(Number);

    const startDateTime = new Date(selectedDate);
    startDateTime.setHours(startHour, startMinute, 0, 0);

    const endDateTime = new Date(selectedDate);
    endDateTime.setHours(endHour, endMinute, 0, 0);
    const now = new Date();

    // Validation
    if (startDateTime < now) {
      newErrors.start = "Start time cannot be in the past.";
      newErrors.end = "End time cannot be in the past.";
    }
    const durationMs = endDateTime.getTime() - startDateTime.getTime();
    const durationMinutes = Math.floor(durationMs / 60000);
    if (startDateTime >= endDateTime) {
      newErrors.start = "Start time must be before end time.";
      newErrors.end = "End time must be after start time.";
    } else {
      if (durationMinutes < 15) {
        newErrors.end = "Minimum duration is 15 minutes.";
      }

      if (durationMinutes > 120) {
        newErrors.end = "Maximum allowed duration is 2 hours.";
      }
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setErrors({});

    const newEvent: InterviewEvent = {
      id: userEventId ?? Date.now(),
      title: "New Interview Slot",
      start: startDateTime,
      end: endDateTime,
      interviewer: "Current User",
      candidate: "To be assigned",
      status: "Pending",
      isUserCreated: true,
    };

    setUserSlot(newEvent);

    setEvents((prevEvents) => {
      if (userEventId) {
        return prevEvents.map((event) =>
          event.id === userEventId ? newEvent : event,
        );
      } else {
        setUserEventId(newEvent.id);
        return [...prevEvents, newEvent];
      }
    });

    setShowModal(false);
    setSelectedDate(startDateTime);
    setEditSlot(null);

    // Format output
    const formattedDate = startDateTime.toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric",
    });
    const formattedStartTime = startDateTime.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
    const formattedEndTime = endDateTime.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });

    const finalOutput = `${formattedDate} ${formattedStartTime}-${formattedEndTime}`;

    // let durationString = "";
    // const hours = Math.floor(durationMinutes / 60);
    // const minutes = durationMinutes % 60;

    // if (hours > 0) durationString += `${hours} hour${hours > 1 ? "s" : ""}`;
    // if (minutes > 0)
    //   durationString += `${hours > 0 ? " " : ""}${minutes} minute${minutes > 1 ? "s" : ""}`;
    // if (!durationString) durationString = "0 minutes";

    onConfirm(
      finalOutput,
      durationMinutes,
      {
        start: timeSlot.startTime,
        end: timeSlot.endTime,
      },
      newEvent,
    );
  };

  const eventStyleGetter = (event: InterviewEvent) => {
    const style: React.CSSProperties = {
      backgroundColor: "#3b82f6",
      borderRadius: "6px",
      color: "white",
      padding: "4px 8px",
      border: "none",
      display: "block",
    };

    // Style user-created slots differently
    if (event.isUserCreated) {
      style.backgroundColor = "#8b5cf6"; // Purple for user-created slots
      style.border = "2px solid #7c3aed";
      if (event.id === userEventId) {
        style.border = "2px solid #fbbf24"; // Gold border for the editable slot
      }
    } else {
      // Style for backend slots
      const status = event.status.toLowerCase();
      if (status.includes("completed")) style.backgroundColor = "#28a745";
      else if (status.includes("cancelled")) style.backgroundColor = "#dc3545";
      else if (status.includes("pending")) {
        style.backgroundColor = "#facc15";
        style.color = "black";
      }
    }

    return { style };
  };

  // Custom Toolbar to hide back button on current week
  const CustomToolbar: any = (toolbar: any) => {
    const goToBack = () => {
      toolbar.onNavigate("PREV");
    };
    const goToNext = () => {
      toolbar.onNavigate("NEXT");
    };
    const goToToday = () => {
      toolbar.onNavigate("TODAY");
    };

    const today = new Date();
    const start = toolbar.date;
    const end = new Date(start);
    end.setDate(start.getDate() + 6);

    const isCurrentWeek =
      today >=
        new Date(start.getFullYear(), start.getMonth(), start.getDate()) &&
      today <= new Date(end.getFullYear(), end.getMonth(), end.getDate());

    const viewNames = ["week", "day"]; // "agenda"

    return (
      <div className="rbc-toolbar">
        <span className="rbc-btn-group">
          {!isCurrentWeek && (
            <button type="button" onClick={goToBack}>
              Back
            </button>
          )}
          <button type="button" onClick={goToToday}>
            Today
          </button>
          <button type="button" onClick={goToNext}>
            Next
          </button>
        </span>
        <span className="rbc-toolbar-label">{toolbar.label}</span>
        <span className="rbc-btn-group" style={{ marginLeft: 16 }}>
          {viewNames.map((name) => (
            <button
              key={name}
              type="button"
              className={toolbar.view === name ? "active" : ""}
              onClick={() => toolbar.onView(name)}>
              {name.charAt(0).toUpperCase() + name.slice(1)}
            </button>
          ))}
        </span>
      </div>
    );
  };

  const handleRangeChange: any = (range: any, view: string) => {
    let start: Date, end: Date;
    if (Array.isArray(range)) {
      // For week/day views, range is an array of dates
      start = range[0];
      end = range[range.length - 1];
    } else if (range.start && range.end) {
      // For month/agenda, range is an object
      start = range.start;
      end = range.end;
    } else {
      // fallback
      start = new Date();
      end = new Date();
    }
    start.setHours(0, 0, 0, 0);
    end.setHours(23, 59, 59, 0);
    const formattedStart: any = moment(start).format("YYYY-MM-DDT00:00:00");
    const formattedEnd: any = moment(end).format("YYYY-MM-DDT23:59:59");
    setCalendarRange({ start: formattedStart, end: formattedEnd });
  };

  const handleRemove = () => {
    setUserSlot(null);
    setUserSlotData(null);
    setEvents((prev) => prev.filter((ev) => !ev.isUserCreated));
    setViewSlot(null);
    setEditSlot(null);
    setSelectedDate(null);
    setShowModal(false);
    flashMessage("Slot removed.", "info");
  };

  return (
    <div>
      <Calendar
        localizer={localizer}
        events={events}
        startAccessor="start"
        endAccessor="end"
        selectable
        onSelectSlot={handleSelectSlot}
        onSelectEvent={handleSelectEvent}
        defaultView={Views.WEEK}
        views={[Views.WEEK, Views.DAY]} // Views.AGENDA
        length={7}
        style={{ height: 650 }}
        eventPropGetter={eventStyleGetter}
        tooltipAccessor={(event: InterviewEvent) =>
          `Interviewer: ${event.interviewer}\nCandidate: ${event.candidate}\nStatus: ${event.status}`
        }
        components={{
          toolbar: CustomToolbar,
        }}
        onRangeChange={handleRangeChange}
        step={240}
        timeslots={1}
      />

      <Modal
        show={showModal}
        onHide={() => {
          setViewSlot(null);
          setEditSlot(null);
          setShowModal(false);
        }}
        centered>
        <Modal.Header closeButton>
          <Modal.Title>
            {editSlot
              ? "Edit Interview Slot"
              : viewSlot
                ? "View Interview Slot"
                : "Add Interview Slot"}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Date</Form.Label>
              <Form.Control
                type="text"
                value={
                  editSlot
                    ? moment(editSlot.start).format("MMMM D, YYYY")
                    : viewSlot
                      ? moment(viewSlot.start).format("MMMM D, YYYY")
                      : selectedDate
                        ? moment(selectedDate).format("MMMM D, YYYY")
                        : ""
                }
                disabled
              />
            </Form.Group>
            <Row>
              <Col>
                <Form.Group className="mb-3">
                  <Form.Label>Start Time</Form.Label>
                  <Form.Control
                    type="time"
                    value={
                      editSlot
                        ? timeSlot.startTime
                        : viewSlot
                          ? `${String(viewSlot.start.getHours()).padStart(2, "0")}:${String(viewSlot.start.getMinutes()).padStart(2, "0")}`
                          : timeSlot.startTime
                    }
                    disabled={viewSlot}
                    onChange={(e) => {
                      if (editSlot) {
                        setTimeSlot({ ...timeSlot, startTime: e.target.value });
                        setErrors((prev) => ({ ...prev, start: undefined }));
                      } else if (!viewSlot) {
                        setTimeSlot({ ...timeSlot, startTime: e.target.value });
                        setErrors((prev) => ({ ...prev, start: undefined }));
                      }
                    }}
                  />
                  {!viewSlot && errors.start && (
                    <Form.Text className="text-danger">
                      {errors.start}
                    </Form.Text>
                  )}
                </Form.Group>
              </Col>
              <Col>
                <Form.Group className="mb-3">
                  <Form.Label>End Time</Form.Label>
                  <Form.Control
                    type="time"
                    value={
                      editSlot
                        ? timeSlot.endTime
                        : viewSlot
                          ? `${String(viewSlot.end.getHours()).padStart(2, "0")}:${String(viewSlot.end.getMinutes()).padStart(2, "0")}`
                          : timeSlot.endTime
                    }
                    disabled={viewSlot}
                    onChange={(e) => {
                      if (editSlot) {
                        setTimeSlot({ ...timeSlot, endTime: e.target.value });
                        setErrors((prev) => ({ ...prev, end: undefined }));
                      } else if (!viewSlot) {
                        setTimeSlot({ ...timeSlot, endTime: e.target.value });
                        setErrors((prev) => ({ ...prev, end: undefined }));
                      }
                    }}
                  />
                  {!viewSlot && errors.end && (
                    <Form.Text className="text-danger">{errors.end}</Form.Text>
                  )}
                </Form.Group>
              </Col>
            </Row>
            {(viewSlot || editSlot) && (
              <Form.Group className="mb-3">
                <Form.Label>Status</Form.Label>
                <Form.Control
                  type="text"
                  value={editSlot ? editSlot.status : viewSlot?.status || ""}
                  disabled
                />
              </Form.Group>
            )}
          </Form>
        </Modal.Body>
        <Modal.Footer>
          {editSlot ? (
            <>
              <Button
                variant="primary"
                onClick={() => {
                  handleSaveTimeSlot();
                }}>
                Update
              </Button>
              <Button variant="danger" onClick={handleRemove}>
                Delete
              </Button>
            </>
          ) : viewSlot ? null : (
            <>
              <Button variant="secondary" onClick={() => setShowModal(false)}>
                Cancel
              </Button>
              <Button variant="primary" onClick={handleSaveTimeSlot}>
                Save
              </Button>
            </>
          )}
        </Modal.Footer>
      </Modal>

      <div className="mt-3">
        {selectedDate && (
          <p>
            Selected:{" "}
            <strong>
              {moment(selectedDate).format("MMMM D, YYYY h:mm A")}
            </strong>
          </p>
        )}

        {/* Information about user-created slots */}
        {userEventId && (
          <div className="mt-2 p-2 bg-light rounded">
            <small className="text-muted">
              <strong>💡 Tip:</strong> Your created slot is highlighted in
              purple with a gold border. Click on it to edit the time. This slot
              will be available for scheduling until you create an interview.
            </small>
          </div>
        )}
      </div>
    </div>
  );
};

export default InterviewSlotsCalendarModal;
