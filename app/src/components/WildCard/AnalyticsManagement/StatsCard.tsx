import React, { useState, useEffect } from "react";
import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import { analyticApi } from "@src/apis/wildcardApis";

type StatMeta = {
  label: string;
  key: string;
  color: string;
  trailColor: string;
  boxClass: string;
};

const statConfig: StatMeta[] = [
  {
    label: "Total Jobs",
    key: "totalJobs",
    color: "rgba(26, 114, 218)",
    trailColor: "#E6F2FF",
    boxClass: "box-1",
  },
  {
    label: "Active Jobs",
    key: "activeJobs",
    color: "rgba(44, 185, 168)",
    trailColor: "#E5FFFC",
    boxClass: "box-2",
  },
  {
    label: "Total Candidates",
    key: "totalCandidates",
    color: "rgba(177, 24, 174)",
    trailColor: "#FFE8FF",
    boxClass: "box-3",
  },
  {
    label: "Total Hiring",
    key: "totalHiring",
    color: "rgba(223, 135, 10)",
    trailColor: "#FFF0DA",
    boxClass: "box-4",
  },
];

export function StatsCard() {
  const [statValues, setStatValues] = useState<Record<string, number>>({
    totalJobs: 0,
    activeJobs: 0,
    totalCandidates: 0,
    totalHiring: 0,
  });

  useEffect(() => {
    // Simulate fetching data from an API
    const fetchStats = async () => {
      // Replace with actual API call
      const { success, data } =
        await analyticApi.hiringManagementDashboardStats();
      if (success) {
        setStatValues((prev) => ({ ...prev, ...data }));
      }
    };

    fetchStats();
  }, []);

  return (
    <div className="card-body">
      <div className="row row-gap-3">
        {statConfig.map(({ label, key, color, trailColor, boxClass }) => (
          <div className="col-lg-3 col-md-6 col-12" key={key}>
            <div
              className={`d-flex gap-3 flex-row align-items-center ${boxClass}`}>
              <CircularProgressbar
                value={100}
                styles={buildStyles({
                  rotation: 0.45,
                  strokeLinecap: "butt",
                  textSize: "16px",
                  pathTransitionDuration: 0.5,
                  pathTransition: "none",
                  pathColor: color,
                  trailColor,
                })}
              />
              <div className="d-flex gap-1 flex-column align-items-center flex-grow-1 justify-content-center">
                <h6 className="mb-0 w-100">{label}</h6>
                <h2 className="m-0 w-100">{statValues[key]}</h2>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
