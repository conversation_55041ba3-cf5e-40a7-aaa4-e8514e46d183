import React, { useEffect, useState } from "react";
import {
  CandidateBasicInterface,
  CandidateInterviewInterface,
  InterviewDetailInterface,
  KeyPairInterface,
} from "@src/redux/interfaces";
import { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { ModalFormInput } from "@src/components/ModalInput/ModalFormInput";
import { useAppDispatch } from "@src/redux/store";
import {
  openDialog,
  closeDialog,
  getAllEmployeeOptions,
  getAllLocationOptions,
  setLoader,
} from "@src/redux/actions";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { candidateInterviewApi } from "@src/apis/wildcardApis";
import flashMessage from "@src/components/FlashMessage";
import { APP_ROUTE } from "@src/constants";
import { useRouter } from "next/router";
import { InterviewModeOptions } from "@src/helper/selectOptions";
import { CandidateInterviewProfileCard } from "./CandidateInterviewProfileCard";
import DialogComponents from "@src/components/DialogComponents";
import { InterviewEvent } from "@src/components/DialogComponents/InterviewSlotsCalendarModal";
import { ConvertToISOString } from "@src/helper/dateTimeHelper";

type ScheduleInterviewType = {
  readonly interview: CandidateInterviewInterface;
  readonly lastInterview: InterviewDetailInterface;
  subdomain: string;
};

export const RescheduleInterview: React.FC<ScheduleInterviewType> = ({
  interview,
  lastInterview,
}) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [state, setState] = useState<KeyPairInterface>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [userSlot, setUserSlot] = useState<InterviewEvent | null>(null);
  const [interviewTimeFieldDisabled, setInterviewTimeFieldDisabled] =
    useState<boolean>(true);
  const [scheduleTime, setScheduleTime] = useState<{
    start: string;
    end: string;
  }>({ start: "", end: "" });
  const employeeOptions = useSelector(
    (state: RootState) => state.employee.options,
  );
  const locationOptions = useSelector(
    (state: RootState) => state.location.options,
  );

  useEffect(() => {
    setState({
      interview_round: lastInterview.interview_round,
      interviewer_id: lastInterview.interviewer_id,
      meeting_link: lastInterview.meeting_link,
      interview_mode_id: lastInterview.interview_mode_id,
      comment: "",
      location_id: lastInterview.interview_location_id,
      phone_number: lastInterview.phone_number,
    });
    if (!["Scheduled", "Shortlist"].includes(interview.status_name)) {
      router.replace(APP_ROUTE.INTERVIEW_MANAGEMENT);
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, []);

  useEffect(() => {
    if (state.interviewer_id) {
      setInterviewTimeFieldDisabled(false);
    } else {
      setInterviewTimeFieldDisabled(true);
    }
  }, [state.interviewer_id]);

  const handleSubmit = async () => {
    await dispatch(setLoader(true));
    const { success, message } = await candidateInterviewApi.addNewRound(
      interview.id,
      {
        interviewer_id: state.interviewer_id,
        interview_at: ConvertToISOString(state.interview_at),
        comment: state.comment,
        reason: state.reason,
        meeting_link: state.meeting_link,
        interview_round: lastInterview.interview_round,
        opportunity_id: lastInterview.opportunity_id,
        interview_mode_id: lastInterview.interview_mode_id,
        phone_number: state.phone_number,
        time_duration: state.time_duration,
        status: 2,
      },
    );
    await dispatch(setLoader(false));
    flashMessage(message, success ? "success" : "error");
    if (success) {
      router.push(APP_ROUTE.INTERVIEW_MANAGEMENT);
    }
  };

  const fetchEmployeeOptions = async (
    search: string,
    showId: number | undefined = undefined,
  ) => {
    showId = search == "" ? interview.interviewer_id : showId;
    await setLoading(true);
    await dispatch(
      getAllEmployeeOptions({
        search: search,
        showId: showId,
        type: "Interviewer",
      }),
    );
    await setLoading(false);
  };

  const fetchLocationOptions = async (
    search: string,
    showId: number | undefined = undefined,
  ) => {
    await setLoading(true);
    await dispatch(getAllLocationOptions({ search: search, showId: showId }));
    await setLoading(false);
  };

  const onInterviewTimeConfirm = async (
    selectedDate: string,
    duration: number,
    scheduleOriginalTime: any,
    slotEvent: InterviewEvent,
  ) => {
    console.log("onInterviewTimeConfirm called with:", {
      selectedDate,
      duration,
    });
    setState((prev) => ({
      ...prev,
      interview_at: selectedDate,
      time_duration: duration,
    }));
    setScheduleTime({
      start: scheduleOriginalTime?.start,
      end: scheduleOriginalTime?.end,
    });
    setUserSlot(slotEvent);
    setInterviewTimeFieldDisabled(false);
    dispatch(closeDialog());
  };

  const handleCalendarModal = () => {
    if (state?.interviewer_id) {
      setInterviewTimeFieldDisabled(true);
      dispatch(
        openDialog({
          config: DialogComponents.INTERVIEW_SLOTS_CALENDAR_MODAL,
          options: {
            beforeCloseCallback: () => {
              setInterviewTimeFieldDisabled(false);
            },
            title: "Schedule Interview",
            onConfirm: onInterviewTimeConfirm,
            interviewerId: state.interviewer_id,
            scheduleOriginalTime: scheduleTime,
            userSlot,
            setUserSlot,
          },
        }),
      );
    } else {
      flashMessage("Please select an interviewer first.", "error");
    }
  };

  let NewFormFields: GlobalInputFieldType[] = [
    {
      name: "interviewer_id",
      label: "Interviewer",
      type: "select",
      dataType: "select",
      placeholder: "Select Interviewer",
      required: ![0, 3, 4, 5, 6].includes(state.status),
      showSearch: true,
      selectEmpty: true,
      options: employeeOptions,
      onSearch: fetchEmployeeOptions,
      loading: loading,
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 1,
      className: "col-sm-12 col-md-4",
    },

    {
      name: "interview_at",
      label: "Interview Time",
      type: "text",
      dataType: "text",
      placeholder: "Interview Date & Time",
      required: !interviewTimeFieldDisabled,
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 2,
      className: "col-sm-12 col-md-4",
      onClick: handleCalendarModal,
      value: state.interview_at,
      defaultValue: state.interview_at,
      disabled: interviewTimeFieldDisabled, // Disable the input field to prevent manual entry
    },

    {
      name: "interview_round",
      label: "Interview Round",
      type: "string",
      dataType: "string",
      placeholder: "Interview Round",
      required: false,
      groupName: "Interview Detail",
      disabled: true,
      groupPosition: 1,
      fieldPosition: 4,
      className: "col-sm-12 col-md-4",
    },

    {
      name: "interview_mode_id",
      label: "Interview Mode",
      type: "select",
      dataType: "select",
      placeholder: "Select Mode",
      options: InterviewModeOptions,
      required: false,
      disabled: true,
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 5,
      className: "col-sm-12 col-md-4",
    },
    {
      name: "phone_number",
      label: "Phone Number(Candidate)",
      type: "mobilenumber",
      dataType: "mobilenumber",
      placeholder: "Enter Phone Number",
      required: true,
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 6,
      className: "col-sm-6 col-md-4",
      minLength: 10,
    },
    {
      name: "location_id",
      label: "Location",
      type: "select",
      dataType: "select",
      placeholder: "Select Location",
      required: true,
      disabled: true,
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 7,
      className: "col-sm-6 col-md-4",
      showSearch: true,
      options: locationOptions,
      onSearch: fetchLocationOptions,
      loading: loading,
    },

    {
      name: "reason",
      label: "Reschedule Reason",
      type: "textarea",
      dataType: "text",
      placeholder: "Add Reschedule Reason",
      tooltipTitle: "Enter the interview reschedule reason",
      required: true,
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 9,
    },
    {
      name: "comment",
      label: "Additional Notes",
      type: "textarea",
      dataType: "text",
      placeholder: "Add Notes",
      tooltipTitle:
        "Enter any additional content or message you wish to include in the email",
      required: false,
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 10,
    },
  ];

  if (state.interview_mode_id == 0) {
    NewFormFields = [
      ...NewFormFields,
      {
        name: "meeting_link",
        label: "Meeting Link",
        type: "text",
        dataType: "websiteurl",
        placeholder: "Enter Meeting Link",
        required: true,
        groupName: "Interview Detail",
        groupPosition: 1,
        fieldPosition: 6,
        className: "col-sm-12 col-md-8",
      },
    ];
  }

  let candidate: CandidateBasicInterface = {
    id: interview.candidate_id,
    name: interview.candidate_name,
    email: interview.candidate_email,
    designation: interview.candidate_designation,
  };
  return (
    <>
      <CandidateInterviewProfileCard candidate={candidate} />
      <ModalFormInput
        buttonTitle="Reschedule" // Title for the submit button
        fields={NewFormFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={handleSubmit} // Function to handle form submission
      />
    </>
  );
};
