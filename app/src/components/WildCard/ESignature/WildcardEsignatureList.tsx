import React from "react";
import { Pagination, Switch } from "antd";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";


type WildcardEsignatureListProps = {
  rows:any[],
  count: number;
  currentPage: number;
  limit: number;
  fetchData: (page: number, limit: number) => void;
  subdomain: string;
  onToggleStatus?: (employee: any) => void;
};

export function WildcardEsignatureList({
  rows,
  count,
  currentPage,
  limit,
  fetchData,
  onToggleStatus,
}: WildcardEsignatureListProps) {
  const candidatesPermissions =
    useEmployeeSelectedPagePermissions("candidates");

  // Handle page change for pagination
  const handlePageChange = (page: number) => {
    fetchData(page, limit);
  };

  // Handle toggle status
  const handleToggleStatus = (employee: any) => {
    if (onToggleStatus) {
      onToggleStatus(employee);
    }
  };

  console.log(  count,
  currentPage,
  limit,);
  



  const hasRows = rows && rows.length > 0;

  const hasCandidateWritePermission = candidatesPermissions.includes("write");

  return (
    <>
      <div
        className={`table-responsive job-request-list ${hasRows ? "" : "no-records"}`}>
        <table
          className="table table-hover dataTable"
          style={{ width: "100%" }}>
          <thead>
            <tr role="row">
              <th className="mw-50px">Sr. No</th>
              <th className="mw-100px">Employee</th>
              <th className="mw-100px">Email</th>
              <th className="mw-100px">CreatedAt</th>
              <th className="mw-80px">Signature</th>
              <th className="mw-80px">Active</th>
            </tr>
          </thead>
          <tbody>
            {hasRows ? (
              rows.map((employee: any, index: number) => (
                <tr key={index}>
                  <th>{index + 1 + (currentPage - 1) * limit}</th>
                  <td>{employee?.employee_name}</td>
                  <td>{employee?.employee_email}</td>
                  <td>
                    {employee.created_at?.strftime("%B %d, %Y %I:%M %p") ??
                      "N/A"}
                  </td>
                  <td>
                      {employee?.signature}
                  </td>
                  <td>
                    <div className="d-flex gap-2 align-items-center">
                      <Switch
                        checked={employee.status === 1}
                        className="switch-theme"
                        onChange={() => handleToggleStatus(employee)}
                        disabled={!hasCandidateWritePermission}
                      />
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr className="no-records">
                <td colSpan={6} className="text-center">
                  No Records Found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <Pagination
        className="mt-4"
        current={currentPage}
        total={count}
        pageSize={limit}
        hideOnSinglePage
        onChange={handlePageChange}
      />
    </>
  );
}
