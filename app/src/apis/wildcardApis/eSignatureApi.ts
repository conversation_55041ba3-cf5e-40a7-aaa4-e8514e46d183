import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { EMPLOYEE_SIGNATURES, EMPLOYEES } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Employee Signature API functions
const getEmployeeSignatures = (params: any) => {
  return API.get(EMPLOYEE_SIGNATURES, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const createEmployeeSignature = (data: any) => {
  return API.post(EMPLOYEE_SIGNATURES, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const updateEmployeeSignature = (id: number, data: any) => {
  return API.patch(`${EMPLOYEE_SIGNATURES}/${id}`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const getActiveEmployees = (params: any) => {
  return API.get(`${EMPLOYEES}/option/all-active-employees`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

export default {
  getEmployeeSignatures,
  createEmployeeSignature,
  updateEmployeeSignature,
  getActiveEmployees,
}