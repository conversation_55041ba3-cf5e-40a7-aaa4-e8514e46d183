"""
Peewee migrations -- 061_create_esign_otps.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migra<PERSON>


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Create esign_otps table."""
    sql_query = """
        CREATE TABLE esign_otps (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            employee_id BIGINT NOT NULL,
            candidate_id BIGINT NOT NULL,
            reason VARCHAR(255),
            otp VARCHAR(10) NOT NULL,
            otp_expire_at DATETIME DEFAULT NULL,
            is_deleted BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            version INTEGER DEFAULT 1,
            <PERSON>OR<PERSON><PERSON><PERSON> (employee_id) REFERENCES employees(id),
            <PERSON>OR<PERSON><PERSON><PERSON> (candidate_id) REFERENCES candidates(id)
        );
    """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Drop esign_otps table."""
    migrator.sql("DROP TABLE IF EXISTS `esign_otps`;")
