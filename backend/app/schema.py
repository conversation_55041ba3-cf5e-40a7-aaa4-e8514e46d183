from pydantic import BaseModel
from typing import List, Any, Optional


class MessageResponse(BaseModel):
    message: str
    success: bool = True
    status_code: int = 200


class SuccessResponse(BaseModel):
    message: str
    success: bool = True
    status_code: int = 200
    data: Optional[Any] = None


class ValidationErrorSchema(BaseModel):
    detail: str


class PaginationResponseModel(BaseModel):
    page: int
    limit: int
    count: int
    rows: List[dict]


class PaginationResponse(BaseModel):
    message: str
    success: bool = True
    status_code: int = 200
    data: PaginationResponseModel
    meta: Optional[Any] = None


class OfferLetterContent(BaseModel):
    content: str
    subject: str
    opportunity_id: int


class OtpVerificationSchema(BaseModel):
    otp: str
