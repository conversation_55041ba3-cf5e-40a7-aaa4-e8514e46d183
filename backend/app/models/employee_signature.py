from app.models.base import ActiveRecord
from peewee import (
    ForeignKeyField,
    DateTimeField,
    BigIntegerField,
    TextField,
    SmallIntegerField,
    SQL,
)
from app.models.mixins import VersioningMixin, SoftDeletedMixin


class EmployeeSignature(ActiveRecord, SoftDeletedMixin, VersioningMixin):
    # to prevent circular import
    from app.models.employee import Employee

    id = BigIntegerField(primary_key=True)
    employee_id = BigIntegerField(null=False)
    signature = TextField(null=True)  # Store base64 encoded signature blob
    status = SmallIntegerField(constraints=[SQL("DEFAULT 1")])  # 1=active, 0=inactive
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    employee = Foreign<PERSON>eyField(Employee, backref="signatures", on_delete="CASCADE", lazy_load=True)

    def info(self):
        return {
            "id": self.id,
            "employee_id": self.employee_id,
            "signature": self.signature,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    class Meta:
        table_name = "employee_signatures"
        indexes = (("employee_id",), False)