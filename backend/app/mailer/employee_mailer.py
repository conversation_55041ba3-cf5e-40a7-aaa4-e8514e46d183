from app.models import Employee, ScheduleInterview, Candidate, Business
from app.utils import AppTemplates, build_subdomain_url
from app.config import API_URL, APP_URL
from app.mailer.base_mailer import BaseMailer
import mimetypes
import os
import requests


class EmployeeMailer(BaseMailer):
    @classmethod
    def reset_password_email(cls, employee: Employee):
        subject = "Recruitease Pro - Password Reset OTP"
        context = {
            "request": None,
            "employee": employee,
            "api_url": API_URL,
            "app_url": APP_URL,
        }
        body = AppTemplates.get_template(
            "mailer/employee_mailer/forgot-password.html"
        ).render(context)
        cls.send_email(subject, employee.email, body, send_to_primary=True)

    @classmethod
    def resend_otp_email(cls, employee: Employee, resend_text: str):
        subject = "Recruitease Pro - New OTP"
        context = {
            "request": None,
            "employee": employee,
            "api_url": API_URL,
            "app_url": APP_URL,
            "resend_text": resend_text,
        }
        body = AppTemplates.get_template(
            "mailer/employee_mailer/resend-otp.html"
        ).render(context)
        cls.send_email(subject, employee.email, body, send_to_primary=True)

    @classmethod
    def new_employee_email(cls, employee: Employee, password: str):
        subject = f"Recruitease Pro - Welcome to {employee.business.name}"
        login_url = build_subdomain_url(APP_URL, employee.business.subdomain)
        context = {
            "request": None,
            "employee": employee,
            "api_url": API_URL,
            "app_url": APP_URL,
            "login_url": login_url,
            "password": password,
        }
        body = AppTemplates.get_template(
            "mailer/employee_mailer/new-employee.html"
        ).render(context)
        cls.send_email(subject, employee.email, body, send_to_primary=True)

    @classmethod
    def interview_scheduled_mail(
        cls, employee: Employee, schedule_interview: ScheduleInterview
    ):
        subject = "Recruitease Pro - Interview Assigned"
        business: Business = employee.business
        candidate: Candidate = schedule_interview.candidate

        attachments = []

        if candidate.resume:
            try:
                response = requests.get(candidate.resume_url)
                filename = os.path.basename(candidate.resume_url)

                # Guess MIME type
                mime_type, _ = mimetypes.guess_type(filename)
                if mime_type is None:
                    mime_type = (
                        "application/octet-stream"  # Default fallback for unknown types
                    )

                file_bytes = response.content
                # Prepare attachments as (filename, bytes, MIME type)
                attachments.append((filename, file_bytes, mime_type))
            except Exception as e:
                print("ERROR", e)

        has_attachment = len(attachments) > 0

        context = {
            "request": None,
            "api_url": API_URL,
            "app_url": APP_URL,
            "candidate": candidate,
            "employee_name": employee.full_name(),
            "business": business,
            "has_attachment": has_attachment,
            "schedule_interview": schedule_interview,
        }

        body = AppTemplates.get_template(
            "mailer/employee_mailer/schedule-interview.html"
        ).render(context)
        cls.send_email(subject, employee.email, body)

    @classmethod
    def interview_reminder_mail(
        cls, employee: Employee, schedule_interview: ScheduleInterview
    ):
        subject = "Recruitease Pro - Interview Reminder"
        business: Business = employee.business
        candidate: Candidate = schedule_interview.candidate

        attachments = []

        if candidate.resume:
            try:
                response = requests.get(candidate.resume_url)
                filename = os.path.basename(candidate.resume_url)

                # Guess MIME type
                mime_type, _ = mimetypes.guess_type(filename)
                if mime_type is None:
                    mime_type = "application/octet-stream"

                file_bytes = response.content

                # Prepare attachments as (filename, bytes, MIME type)
                attachments.append((filename, file_bytes, mime_type))
            except Exception as e:
                print("ERROR", e)

        has_attachment = len(attachments) > 0

        context = {
            "request": None,
            "api_url": API_URL,
            "app_url": APP_URL,
            "candidate": candidate,
            "employee_name": employee.full_name(),
            "business": business,
            "has_attachment": has_attachment,
            "schedule_interview": schedule_interview,
        }

        body = AppTemplates.get_template(
            "mailer/employee_mailer/interview-reminder.html"
        ).render(context)

        cls.send_email(subject, employee.email, body, attachments)

    @classmethod
    def send_esign_otp_email(
        cls,
        candidate: Candidate,
        employee: Employee,
        otp: str,
    ):
        """
        Sends an OTP email for e-signature verification to the candidate.
        """
        business: Business = candidate.business
        subject = f"Recruitease Pro - {business.name} - OTP Verification"

        context = {
            "employee": employee,
            "business": business,
            "otp": otp,
        }

        # Render the email template using the context
        body = AppTemplates.get_template(
            "mailer/offer_letter_mailer/verify_email.html"
        ).render(context)

        # Send the email (no attachments for OTP)
        cls.send_email(subject, employee.email, body)
