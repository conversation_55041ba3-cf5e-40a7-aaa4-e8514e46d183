from fastapi import APIRouter, Depends, HTTPException, status, Query
from app.schema import SuccessResponse
from app.helper import Wildcard<PERSON>uthHelper
from datetime import datetime, timedelta
from app.schema import SuccessResponse
from app.models import (
    Employee,
    Candidate,
    Opportunity,
    Business,
    ScheduleInterview,
    Node,
    Permission,
    BusinessEmployeeRolePermission,
    CandidateInterview,
)
from peewee import fn, JOIN
from typing import Optional
from app.models.helper import ScheduleInterviewHelper, ApiKeyUsageHelper

# Initialize the APIRouter with a tag for categorizing endpoints in the documentation.
router = APIRouter(
    prefix="/dashboard",
    tags=["Wildcard Dashboard API"],
    dependencies=[Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.get_current_auth_token),
    ],
)

# ------------------------------ functions ------------------------------


@router.get(
    "",
    summary="List of Dashboard Stats",
    description="List of Dashboard Stats.",
    response_model=SuccessResponse,
)
async def get_dashboard_stats(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Retrieve dashboard statistics.

    Args:
    - user: The authenticated admin user, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of stats.
    """
    try:
        # Define the start and end of the last week
        today = datetime.now()
        start_of_week = today - timedelta(days=today.weekday() + 7)
        end_of_week = start_of_week + timedelta(days=7)
        is_admin = current_employee.is_admin
        current_employee_id = current_employee.id

        employee_role_id = current_employee.employee_role_id
        read_permission = Permission.get(name="read")
        interview_node = Node.get(unique_key="employee_interviews", type="EmployeeNode")
        candidate_node = Node.get(unique_key="employee_candidates", type="EmployeeNode")
        job_node = Node.get(unique_key="employee_opportunities", type="EmployeeNode")

        total_completed_interviews = 0
        total_running_interviews = 0

        base_permission_query = BusinessEmployeeRolePermission.select().where(
            BusinessEmployeeRolePermission.employee_role_id == employee_role_id,
            BusinessEmployeeRolePermission.permission_id == read_permission.id,
            BusinessEmployeeRolePermission.business_id == business.id,
        )

        has_interview_permission = base_permission_query.where(
            BusinessEmployeeRolePermission.node_id == interview_node.id
        ).exists()
        has_candidate_permission = base_permission_query.where(
            BusinessEmployeeRolePermission.node_id == candidate_node.id
        ).exists()
        has_job_permission = base_permission_query.where(
            BusinessEmployeeRolePermission.node_id == job_node.id
        ).exists()

        # Fetch your dashboard statistics from your data source.

        if is_admin:
            # basic job Query of active jobs
            job_query = Opportunity.select().where(
                Opportunity.status == 1,
                Opportunity.business_id == business.id,
            )

            # Query to count active jobs
            jobs_active = job_query.count()

            # Query to count total jobs added last week
            total_jobs_last_week = job_query.where(
                Opportunity.created_at.between(start_of_week, end_of_week)
            ).count()

            # interview basic jobs query
            job_query = ScheduleInterview.select().where(
                ScheduleInterview.business_id == business.id
            )

            total_interviews = job_query.count()
            total_interviews_last_week = job_query.where(
                ScheduleInterview.created_at.between(start_of_week, end_of_week)
            ).count()

        else:
            # Query to count active jobs
            job_query = Opportunity.select().where(
                Opportunity.status
                == 1 & Opportunity.business_id
                == business.id
                & (
                    (Opportunity.created_by_id == current_employee_id)
                    | (Opportunity.contact_person_id == current_employee_id)
                )
            )

            jobs_active = job_query.count()

            # Query to count total jobs added last week
            total_jobs_last_week = job_query.where(
                Opportunity.created_at.between(start_of_week, end_of_week)
            ).count()

            # interview base query
            interview_query = ScheduleInterview.select().where(
                (ScheduleInterview.business_id == business.id)
                & (
                    (ScheduleInterview.created_by_id == current_employee_id)
                    | (ScheduleInterview.interviewer_id == current_employee_id)
                )
            )

            total_interviews = interview_query.count()
            total_interviews_last_week = interview_query.where(
                ScheduleInterview.interview_at.between(start_of_week, end_of_week)
            ).count()
            total_running_interviews = interview_query.where(
                ScheduleInterview.status.in_([1, 2, 6])
            ).count()
            total_completed_interviews = interview_query.where(
                ScheduleInterview.status.in_([0, 3, 4, 5])
            ).count()

        # Query to count total candidates
        total_candidates = (
            Candidate.select()
            .where(Candidate.business_id == business.id, Candidate.is_deleted == False)
            .count()
        )

        # Query to count total shortlisted candidates
        total_shortlisted = (
            Candidate.select()
            .where(Candidate.status == 2, Candidate.business_id == business.id)
            .count()
        )

        # Query to count total candidates added last week
        total_candidates_last_week = (
            Candidate.select()
            .where(
                Candidate.created_at.between(start_of_week, end_of_week),
                Candidate.business_id == business.id,
                Candidate.is_deleted == False,
            )
            .count()
        )

        # Query to count total shortlisted candidates added last week
        total_shortlisted_last_week = (
            Candidate.select()
            .where(
                Candidate.status == 2,
                Candidate.created_at.between(start_of_week, end_of_week),
                Candidate.business_id == business.id,
                Candidate.is_deleted == False,
            )
            .count()
        )

        stats = {
            "jobs_active": jobs_active,
            "total_candidates": total_candidates,
            "total_shortlisted": total_shortlisted,
            "total_jobs_last_week": total_jobs_last_week,
            "total_candidates_last_week": total_candidates_last_week,
            "total_shortlisted_last_week": total_shortlisted_last_week,
            "total_interviews": total_interviews,
            "total_interviews_last_week": total_interviews_last_week,
            "has_candidate_permission": has_candidate_permission,
            "has_interview_permission": has_interview_permission,
            "has_job_permission": has_job_permission,
            "total_completed_interviews": total_completed_interviews,
            "total_running_interviews": total_running_interviews,
            "total_credits": business.total_credits,
            "remaining_credits": business.remaining_credits,
        }

        # Return the data using the defined response model
        return SuccessResponse(
            message="Dashboard statistics fetched successfully.", data=stats
        )
    except Exception as e:
        print("e --------", str(e))
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/top_jobs",
    summary="List of Top 10 Jobs",
    description="List of Top 10 Jobs.",
    response_model=SuccessResponse,
)
async def get_top_jobs(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Retrieve the top 10 jobs.

    Args:
    - user: The authenticated admin user, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of top 10 jobs.
    """
    try:
        # Fetch top 10 jobs from the opportunity table
        if current_employee.is_admin:
            top_jobs_query = (
                Opportunity.select(
                    Opportunity.id.alias("job_id"),
                    Opportunity.title.alias("job_title"),
                    Opportunity.created_at.alias("posted_on"),
                    fn.COUNT(CandidateInterview.id).alias("applicants"),
                    Opportunity.status,
                    Opportunity.business_id,
                )
                .join(
                    CandidateInterview,
                    JOIN.LEFT_OUTER,
                    on=(CandidateInterview.opportunity_id == Opportunity.id),
                )
                .where(Opportunity.business_id == business.id, Opportunity.status == 1)
                .group_by(Opportunity.id)
                .order_by(Opportunity.created_at.desc())
                .limit(10)
            )
        else:
            top_jobs_query = (
                Opportunity.select(
                    Opportunity.id.alias("job_id"),
                    Opportunity.title.alias("job_title"),
                    Opportunity.created_at.alias("posted_on"),
                    fn.COUNT(CandidateInterview.id).alias("applicants"),
                    Opportunity.status,
                    Opportunity.business_id,
                    Opportunity.created_by_id,
                    Opportunity.contact_person_id,
                )
                .join(
                    CandidateInterview,
                    JOIN.LEFT_OUTER,
                    on=(CandidateInterview.opportunity_id == Opportunity.id),
                )
                .where(
                    (Opportunity.status == 1)
                    & (Opportunity.business_id == business.id)
                    & (
                        (Opportunity.created_by_id == current_employee.id)
                        | (Opportunity.contact_person_id == current_employee.id)
                    )
                )
                .group_by(Opportunity.id)
                .order_by(Opportunity.created_at.desc())
                .limit(10)
            )

        top_jobs = []
        for job in top_jobs_query:
            top_jobs.append(
                {
                    "job_id": job.job_id,
                    "job_title": job.job_title,
                    "posted_on": job.posted_on,
                    "applicants": job.applicants,
                    "status": job.status,
                }
            )

        # Return the data using the defined response model
        return SuccessResponse(
            message="Top 10 jobs fetched successfully.", data=top_jobs
        )
    except Exception as e:
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/stats",
    summary="List of Dashboard Stats",
    description="List of Dashboard Stats.",
    response_model=SuccessResponse,
)
async def get_dashboard_stats(
    type: Optional[str] = Query(
        None, description="Filter by type (monthly, weekly, yearly)"
    ),
    date: Optional[str] = Query(None, description="Selected date in YYYY-MM-DD format"),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Retrieve dashboard statistics.

    Args:
    - type: Type of statistics to fetch (monthly, weekly, yearly).
    - date: The date used for fetching statistics, in YYYY-MM-DD format.
    - user: The authenticated admin user, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of stats.
    """
    try:
        # Convert date from string to datetime object, if provided
        if date:
            try:
                current_date = datetime.strptime(date, "%Y-%m-%d")
            except ValueError:
                raise HTTPException(
                    status_code=400, detail="Invalid date format. Use YYYY-MM-DD."
                )
        else:
            current_date = datetime.today()  # Use today's date if no date is provided

        # Validate type
        if type not in ["weekly", "monthly", "daily", "yearly"]:
            raise HTTPException(
                status_code=400,
                detail="Invalid type. Must be 'weekly', 'monthly', 'daily', or 'yearly'.",
            )

        # Get candidate counts

        labels, counts, title, xLabel, yLabel = (
            ScheduleInterviewHelper.get_interviews_count_by_grouping(
                current_date,
                type,
                business_id=business.id,
                employee_id=current_employee.id,
            )
        )
        bar_stats = {
            "label": labels,
            "data": [counts.get(label, 0) for label in labels],
            "title": title,
            "xLabel": xLabel,
            "yLabel": yLabel,
        }
        # Get candidate status counts
        pie_stats_data, title = ScheduleInterviewHelper.get_interviews_status_counts(
            business_id=business.id, employee_id=current_employee.id
        )

        pie_stats = {
            "data": pie_stats_data,
            "title": title,
        }

        # total_interviews = interview_query.count()

        stats = {
            "bar_stats": bar_stats,
            "pie_stats": pie_stats,
            "stats_type": type,
        }

        # Return the data using the defined response model
        return SuccessResponse(message="Statistics retrieved successfully", data=stats)
    except Exception as e:
        print("exception in stats  ", e)
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/api-usage-stats",
    summary="List of api usage stats",
    description="List of api usage stats",
    response_model=SuccessResponse,
)
async def get_dashboard_stats(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Retrieve dashboard statistics.

    Args:
    - user: The authenticated admin user, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of stats.
    """
    try:
        # Convert date from string to datetime object, if provided

        pie_stats_data, title = ApiKeyUsageHelper.get_usage_stats(
            business_id=business.id
        )

        pie_stats = {
            "data": pie_stats_data,
            "title": title,
        }

        # total_interviews = interview_query.count()

        stats = {
            "pie_stats": pie_stats,
        }

        # Return the data using the defined response model
        return SuccessResponse(message="Statistics retrieved successfully", data=stats)
    except Exception as e:
        print("exception in stats  ", e)
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
