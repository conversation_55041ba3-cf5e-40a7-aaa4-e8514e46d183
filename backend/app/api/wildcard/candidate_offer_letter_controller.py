from fastapi import APIRouter, Depends, Body, Request
from app.schema import OfferLetterContent, SuccessResponse, OtpVerificationSchema
from app.exceptions import RecordNotFoundException, HTTPException
from app.helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PdfHelper
from app.models import (
    Candidate,
    Employee,
    Business,
    EmailTemplate,
    CandidateOfferLetter,
    CandidateInterview,
    EsignOtp,
)
from app.uploader import OfferLetterUploader
from app.tasks import CandidateTask
from datetime import datetime
from jinja2 import Template
import logging
from datetime import timedelta
import random
from pytz import UTC
from app.mailer import EmployeeMailer

router = APIRouter(
    prefix="/candidates/{id}/offer_letter",
    tags=["Wildcard Candidates Offer Letter API"],
    dependencies=[
        Depends(WildcardAuthHelper.get_current_auth_token),
        Depends(WildcardAuthHelper.validate_subdomain),
    ],
)


# Helper function to get candidate by ID
async def get_candidate(
    id: int,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
) -> Candidate:
    """
    Retrieve a candidate by its ID.

    Args:
        id (int): The ID of the candidate to retrieve.
        business (Business): The current business, provided by dependency injection.

    Returns:
        Candidate: The candidate object if found.

    Raises:
        RecordNotFoundException: If the candidate does not exist.
    """
    candidate = Candidate.get_or_none(id=id, business_id=business.id, is_deleted=0)
    if not candidate:
        raise RecordNotFoundException(message="Candidate does not exist")

    return candidate


@router.get("/templates", response_model=SuccessResponse)
async def get_offer_letter_preview(
    candidate: Candidate = Depends(get_candidate),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Generate and return the offer letter HTML content for the given candidate.
    """
    try:
        # Fetch the email template by ID (e.g. ID = 4)
        email_template = EmailTemplate.get_or_none(id=3)
        if not email_template:
            raise RecordNotFoundException(message="EmailTemplate does not exist")
        if email_template.is_default == 0 and email_template.business_id != business.id:
            raise RecordNotFoundException(message="EmailTemplate does not exist")

        # Render the email template using Jinja2
        template = Template(email_template.email_body)
        rendered_html = template.render(
            candidate={
                "name": candidate.name,
                "email": candidate.email,
                "opportunity": {"title": candidate.Opportunity.title},
            },
            business={"name": business.name},
            current_date=datetime.utcnow().strftime("%d %B, %Y"),
        )

        # Remove unnecessary newlines for cleaner output
        rendered_html = rendered_html.replace("\n", "")

        return SuccessResponse(
            message="Offer letter template rendered successfully",
            data={"html": rendered_html},
        )

    except RecordNotFoundException as e:
        logging.warning(f"Template not found: {e}")
        raise HTTPException(status_code=404, detail="Template not found")

    except Exception as e:
        logging.exception("Unexpected error while rendering offer letter template")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/send",
    summary="Send Email Template",
    description="Send Email Template.",
    response_model=SuccessResponse,
)
async def send_offer_letter(
    candidate: Candidate = Depends(get_candidate),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    data: OfferLetterContent = Body(...),
):
    try:
        file = PdfHelper.generate_pdf_uploadfile(
            context=data.content, filename="offer_letter.pdf"
        )
        uploader = OfferLetterUploader(
            file, file.filename, f"{candidate.id}/{int(datetime.now().timestamp())}"
        )
        uploaded_path = await uploader.upload()
        can_offer_letter = CandidateOfferLetter.create(
            candidate_id=candidate.id,
            created_by_id=current_employee.id,
            opportunity_id=data.opportunity_id,
            letter=uploaded_path["file_path"],
        )

        interview = CandidateInterview.get_or_none(
            candidate_id=candidate.id,
            opportunity_id=data.opportunity_id,
        )
        interview.offer_sent = True
        interview.save()

        CandidateTask.send_offer_letter_email.delay(
            candidate.id, can_offer_letter.id, data.subject
        )
        return SuccessResponse(message="Offer letter send successfully")
    except RecordNotFoundException as e:
        logging.warning(f"Template not found: {e}")
        raise HTTPException(status_code=404, detail="Template not found")

    except Exception as e:
        logging.exception("Unexpected error while rendering offer letter template")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/esign/otp/generate",
    summary="Generate OTP Verification",
    description="Generate OTP for e-sign verification.",
    response_model=SuccessResponse,
)
async def generate_esign_otp(
    request: Request,
    candidate: Candidate = Depends(get_candidate),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Generate or update an OTP for e-signature functionality with default reason.
    """
    try:
        # Try to get existing OTP record for this employee-candidate pair
        esign_otp = EsignOtp.get_or_none(
            employee_id=current_employee.id, candidate_id=candidate.id
        )

        if not esign_otp:
            # Update the OTP & expiry
            esign_otp.generate_otp()
        else:
            # Create a new entry, then generate the OTP
            esign_otp = EsignOtp.create(
                employee_id=current_employee.id,
                candidate_id=candidate.id,
                reason="e-sign verification for offer letter",
                otp="000000",
                otp_expire_at=datetime.now(UTC),
            )
            esign_otp.generate_otp()

        # Send the OTP email to the employee
        EmployeeMailer.send_esign_otp_email(
            candidate=candidate, employee=current_employee, otp=esign_otp.otp
        )

        return SuccessResponse(
            message="OTP generated and sent successfully",
        )

    except Exception as e:
        logging.exception("Failed to generate and send OTP")
        raise HTTPException(status_code=500, detail="Failed to generate and send OTP")


@router.post(
    "/esign/otp/verify",
    summary="Verify OTP",
    description="Verify the e-sign OTP for the candidate by the current employee.",
    response_model=SuccessResponse,
)
async def verify_esign_otp(
    data: OtpVerificationSchema = Body(...),
    candidate: Candidate = Depends(get_candidate),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Verify the submitted OTP for the e-signature process.
    """
    try:
        esign_otp = EsignOtp.get_or_none(
            employee_id=current_employee.id, candidate_id=candidate.id
        )

        if not esign_otp:
            raise HTTPException(status_code=404, detail="No OTP record found.")

        # Ensure otp_expire_at is timezone-aware before comparing
        otp_expiry = esign_otp.otp_expire_at
        if otp_expiry.tzinfo is None:
            otp_expiry = otp_expiry.replace(tzinfo=UTC)

        if otp_expiry < datetime.now(UTC):
            raise HTTPException(status_code=400, detail="OTP has expired.")

        if esign_otp.otp != data.otp:
            raise HTTPException(status_code=400, detail="Invalid OTP.")

        return SuccessResponse(message="OTP verified successfully")

    except HTTPException:
        raise
    except Exception as e:
        logging.exception("Failed to verify OTP")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/esign/otp/resend",
    summary="Resend OTP",
    description="Resend the e-sign OTP for the candidate to the current employee.",
    response_model=SuccessResponse,
)
async def resend_esign_otp(
    candidate: Candidate = Depends(get_candidate),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Resend the OTP for e-signature functionality to the current employee.
    This endpoint regenerates the OTP and sends it via email.
    """
    try:
        # Check if there's an existing OTP record for this employee-candidate pair
        esign_otp = EsignOtp.get_or_none(
            employee_id=current_employee.id, candidate_id=candidate.id
        )

        if not esign_otp:
            # If no existing record, create a new one
            esign_otp = EsignOtp.create(
                employee_id=current_employee.id,
                candidate_id=candidate.id,
                reason="e-sign verification for offer letter",
                otp="000000",
                otp_expire_at=datetime.now(UTC),
            )

        # Generate a new OTP and update expiry time
        esign_otp.generate_otp()

        # Send the new OTP email to the employee
        EmployeeMailer.send_esign_otp_email(
            candidate=candidate, employee=current_employee, otp=esign_otp.otp
        )

        return SuccessResponse(
            message="OTP has been resent successfully.",
        )

    except Exception as e:
        logging.exception("Failed to resend OTP")
        raise HTTPException(status_code=500, detail="Failed to resend OTP")
