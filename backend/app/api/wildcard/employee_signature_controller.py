from fastapi import (
    APIRouter,
    Depends,
    Query,
    HTTPException,
    status,
    Body,
    Request,
    Form,
)
from app.schema import PaginationResponse, SuccessResponse
from app.models import Employee, EmployeeSignature
from app.validations import StringValidate
from app.exceptions import RecordNotFoundException
from app.helper import Wildcard<PERSON><PERSON><PERSON>elper
from app.constants import <PERSON>de<PERSON>ame, PermissionName
from typing import Optional
from peewee import fn
import logging

# Initialize the APIRouter with a tag for categorizing endpoints in the documentation.
router = APIRouter(
    prefix="/employee-signatures",
    tags=["Wildcard Employee Signature API"],
    dependencies=[Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.get_current_auth_token),
    ],
)


def get_employee_signature(id: int):
    """
    Dependency to get an employee signature by ID.
    
    Args:
        id (int): The ID of the employee signature.
        
    Returns:
        EmployeeSignature: The employee signature object.
        
    Raises:
        RecordNotFoundException: If the employee signature is not found.
    """
    employee_signature = EmployeeSignature.get_or_none(EmployeeSignature.id == id)
    if not employee_signature:
        raise RecordNotFoundException("Employee signature not found")
    return employee_signature


@router.get(
    "",
    summary="Get Employee Signatures",
    description="Retrieve a paginated list of employee signatures.",
    response_model=PaginationResponse,
)
def get_employee_signatures(
    request: Request,
    page: int = Query(1, gt=0),
    search: Optional[str] = Query(
        None, description="Search query based on employee name or email"
    ),
    limit: int = Query(10, gt=0, le=100),
    employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Retrieve a paginated list of employee signatures.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of signatures per page. Defaults to 10.
        search (Optional[str]): Search term to filter by employee name or email.
        employee (Employee): The current employee, provided by dependency injection.

    Returns:
        PaginationResponse: Paginated list of employee signatures.
    """
    try:
        # Skip permission check for now to debug
        WildcardAuthHelper.has_route_permission(
            request=request,
            node_key=NodeName.EMPLOYEE_SIGNATURES,
            perm_names=PermissionName.READ_ONLY,
        )

        # Ensure the table exists
        try:
            EmployeeSignature.create_table(safe=True)
        except Exception as table_error:
            logging.warning(f"Could not create employee_signatures table: {str(table_error)}")

        # Simple query first - get all signatures for this business
        all_signatures = (
            EmployeeSignature.select()
            .join(Employee, on=(EmployeeSignature.employee_id == Employee.id))
            .where(Employee.business_id == employee.business_id)
        )

        if search:
            search = search.strip().lower()
            search_filter = (
                (fn.lower(Employee.first_name).contains(search))
                | (fn.lower(Employee.last_name).contains(search))
                | (fn.lower(Employee.email).contains(search))
                | (
                    fn.lower(
                        fn.CONCAT(Employee.first_name, " ", Employee.last_name)
                    ).contains(search)
                )
            )
            all_signatures = all_signatures.where(search_filter)


        total_count = all_signatures.count()


        offset = (page - 1) * limit
        signatures = all_signatures.offset(offset).limit(limit)


        signature_list = []
        for signature in signatures:
            try:
                # Get basic signature info
                signature_data = {
                    "id": signature.id,
                    "employee_id": signature.employee_id,
                    "signature": signature.signature,
                    "status": signature.status,
                    "created_at": signature.created_at.isoformat() if signature.created_at else None,
                    "updated_at": signature.updated_at.isoformat() if signature.updated_at else None,
                }

                try:
                    emp = signature.employee
                    signature_data["employee_name"] = f"{emp.first_name or ''} {emp.last_name or ''}".strip()
                    signature_data["employee_email"] = emp.email or ""
                except Exception as emp_error:
                    logging.warning(f"Could not load employee for signature {signature.id}: {str(emp_error)}")
                    signature_data["employee_name"] = "Unknown"
                    signature_data["employee_email"] = ""

                signature_list.append(signature_data)
            except Exception as sig_error:
                logging.error(f"Error processing signature {signature.id}: {str(sig_error)}")
                continue

        return PaginationResponse(
            message="Employee signatures retrieved successfully",
            data={
                "rows": signature_list,
                "count": total_count,
                "page": page,
                "limit": limit,
            },
        )
    except Exception as e:
        logging.error(f"Exception in get employee signatures: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving employee signatures",
        )

@router.post(
    "",
    summary="Create Employee Signature",
    description="Create a new employee signature.",
    response_model=SuccessResponse,
)
async def create_employee_signature(
    request: Request,
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Endpoint to create a new employee signature via form data.

    Args:
        current_employee (Employee): The current employee.

    Returns:
        SuccessResponse: A response indicating success or failure.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_SIGNATURES,
        perm_names=PermissionName.READ_WRITE,
    )
    
    try:
        form = await request.form()

        employee_id_form = form.get("employee_id")

        if employee_id_form and employee_id_form.strip():
            try:
                employee_id = int(employee_id_form)
                target_employee = Employee.get_or_none(
                    Employee.id == employee_id,
                    Employee.business_id == current_employee.business_id
                )
                if not target_employee:
                    raise ValueError("Employee not found")
            except ValueError as ve:
                if "invalid literal" in str(ve):
                    raise ValueError(f"Invalid employee ID format: {employee_id_form}")
                raise ve
        else:
            # Default to current employee
            employee_id = current_employee.id
            target_employee = current_employee

        # Get signature file
        signature_file = form.get("file")
        if not signature_file:
            raise ValueError("Signature file is required")

        # Read file content and convert to base64
        import base64
        file_content = await signature_file.read()
        signature_base64 = base64.b64encode(file_content).decode('utf-8')
        signature_data_url = f"data:{signature_file.content_type};base64,{signature_base64}"

        # Get status from form, default to active (1)
        status_form = form.get("status")

        if status_form and status_form.strip():
            try:
                status = int(status_form)
                if status not in [0, 1]:
                    raise ValueError("Status must be 0 (inactive) or 1 (active)")
            except ValueError as ve:
                if "invalid literal" in str(ve):
                    raise ValueError(f"Invalid status format: {status_form}")
                raise ve
        else:
            status = 1

        existing_signature = EmployeeSignature.get_or_none(
            EmployeeSignature.employee_id == employee_id
        )
        if existing_signature:
            # Update existing signature instead of creating new one
            existing_signature.signature = signature_data_url
            existing_signature.status = status
            existing_signature.save()

            return SuccessResponse(
                message="Employee signature updated successfully.",
                data=existing_signature.info()
            )

        # Create new employee signature
        new_signature = EmployeeSignature.create(
            employee_id=employee_id,
            signature=signature_data_url,
            status=status,
        )

        return SuccessResponse(
            message="Employee signature created successfully.",
            data=new_signature.info()
        )
    except Exception as e:
        logging.error(f"Exception in create employee signature: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while creating the employee signature",
        )
@router.patch(
    "/{id}",
    summary="Update Employee Signature",
    description="Update an existing employee signature",
    response_model=SuccessResponse,
)
async def update_employee_signature(
    request: Request,
    signature: EmployeeSignature = Depends(get_employee_signature),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    body: dict = Body(
        ...,
        example={
            "signature": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
            "status": 1,
        },
    ),
):
    """
    Update an existing employee signature.

    Args:
        signature (EmployeeSignature): The signature object to update.
        current_employee (Employee): The current employee, provided by dependency injection.
        body (dict): The request body containing updated signature information.

    Returns:
        SuccessResponse: A response indicating success or failure.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_SIGNATURES,
        perm_names=PermissionName.READ_WRITE,
    )
    try:
        # Verify that the signature belongs to an employee in the same business
        if signature.employee.business_id != current_employee.business_id:
            raise ValueError("Access denied")

        # Update signature if provided
        if "signature" in body:
            signature.signature = body["signature"]

        # Update status if provided
        if "status" in body:
            signature_status = body["status"]
            if signature_status not in [0, 1]:
                raise ValueError("Status must be 0 (inactive) or 1 (active)")
            signature.status = signature_status

        # Save the updated signature
        signature.save()

        return SuccessResponse(
            message="Employee signature updated successfully.",
            data=signature.info()
        )
    except Exception as e:
        logging.error(f"Exception in update employee signature: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating the employee signature",
        )
    



@router.get(
    "/option/all-active-signatures",
    summary="Get All Active Employees Signature as Options",
    description="Retrieve a list of all active employees signature within the same business.",
    response_model=SuccessResponse,
)
async def get_all_active_employees(
    request: Request,
    search: Optional[str] = Query(
        None, description="Search term to filter opportunities"
    ),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    Retrieve a list of all active employees within the same business.

    Args:
        search (Optional[str]): Search term to filter Employee first name.
        business (Business): The business object retrieved from the database based on the subdomain.
        current_employee (Employee): The currently authenticated employee.

    Returns:
        SuccessResponse: List of all active employees within the same business.
    """
    try:
        employees = (
            EmployeeSignature.select()
            .where(
                Employee.business_id == business.id,
                Employee.status == 1,
            )
            .order_by(Employee.first_name)
        )

        if search:
            search = search.strip().lower()
            employees = employees.where(fn.LOWER(Employee.first_name).contains(search))
        rows = [
            {
                "label": f"{record.full_name()} - {record.email}",
                "value": record.id,
            }
            for record in employees
        ]

        return SuccessResponse(data=rows, message="Options fetched successfully")
    except Exception as e:
        logging.error(f"Exception in employee list: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")
