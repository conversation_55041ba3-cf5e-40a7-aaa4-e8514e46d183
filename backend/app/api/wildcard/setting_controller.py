from fastapi import APIRouter, Query, Body, Request, Depends
from app.schema import SuccessResponse
from app.exceptions import CustomValidationError
from app.models import (
    Business,
    Permission,
    Node,
    EmployeeRole,
    BusinessEmployeeRolePermission,
    Employee,
)
from app.helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Optional
from app.constants import ConstantMessages
import logging

# Create the router
router = APIRouter(prefix="/settings", tags=["Wildcard Settings"])


@router.get(
    "/permissions",
    summary="WildCard Permission list",
    description="Retrieve a comprehensive list of permissions for wildcard subdomains.",
    response_model=SuccessResponse,
)
async def permissions(
    role_id: Optional[int] = Query(None, description="Employee Role Id"),
):
    """
    Retrieve a list of all permissions available in the system.

    This endpoint fetches all records from the Permission model, processes each
    record to obtain its detailed information, and returns the data in a structured
    response format. The purpose of this endpoint is to provide a comprehensive list
    of permissions that can be used to validate the routes accessible within wildcard
    subdomains.

    Returns:
        SuccessResponse: A response object containing the list of permissions and a success message.
    """
    records = Permission.select()
    rows = [record.info() for record in records]
    dependent_permissions = Node.get_employee_dependent_permissions()
    disabled_permissions = Node.get_employee_disabled_permissions(role_id=role_id)

    data = {
        "permission_rows": rows,
        "dependent_permissions": dependent_permissions,
        "disabled_permissions": disabled_permissions,
    }

    return SuccessResponse(data=data, message="Data fetched successfully")


@router.get(
    "/nodes",
    summary="WildCard Nodes list",
    description="Retrieve a comprehensive list of nodes for wildcard subdomains.",
    response_model=SuccessResponse,
)
async def nodes(
    employee_role_id: Optional[int] = Query(None, description="Employee Role ID"),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve a list of all nodes available in the system.

    This endpoint fetches all records from the Permission model, processes each
    record to obtain its detailed information, and returns the data in a structured
    response format. The purpose of this endpoint is to provide a comprehensive list
    of nodes that can be used to validate the routes accessible within wildcard
    subdomains.

    Returns:
        SuccessResponse: A response object containing the list of nodes and a success message.
    """
    if not employee_role_id:
        raise CustomValidationError(error=ConstantMessages.EMPLOYEE_ROLE_REQUIRED)
    records = Node.get_permissions_list_employee_nodes(employee_role_id, business.id)

    return SuccessResponse(data=records, message="Data fetched successfully")


@router.put(
    "/nodes/{id}",
    summary="Update Node Permissions",
    description="Update the permissions for a given node and employee role, ensuring the node and role exist.",
    response_model=SuccessResponse,
    dependencies=[
        Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.get_current_auth_token),
        Depends(WildcardAuthHelper.employee_is_super_admin)
    ],
)
async def nodes(
    id: int,
    body: dict = Body(
        ...,
        example={
            "employee_role_id": 2,
            "permission_ids": [1, 2, 3],
        },
    ),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    
):
    """
    Update the permissions for a given node and employee role.

    This endpoint updates the permissions associated with a specific node and
    employee role. It checks if the node exists and belongs to type "EmployeeNode",
    and if the employee role is valid. It then updates the permissions by adding
    new ones and removing the ones no longer needed.

    Args:
        id (int): The ID of the node to update.
        body (dict): A dictionary containing the `employee_role_id` and a list of
                     `permission_ids`.

    Returns:
        SuccessResponse: A response object indicating the update was successful.
    """
    node = Node.get_or_none(Node.id == id, Node.type == "EmployeeNode")
    if not node:
        raise CustomValidationError(error=ConstantMessages.NODE_REQUIRED)

    employee_role_id = body.get("employee_role_id")
    if not employee_role_id:
        raise CustomValidationError(error=ConstantMessages.EMPLOYEE_ROLE_REQUIRED)

    permission_ids = set(body.get("permission_ids"))

    employee_role = EmployeeRole.get_or_none(EmployeeRole.id == employee_role_id)
    if not employee_role:
        raise CustomValidationError(error=ConstantMessages.INVALID_EMPLOYEE_ROLE)

    base_query = BusinessEmployeeRolePermission.select().where(
        (BusinessEmployeeRolePermission.node_id == node.id)
        & (BusinessEmployeeRolePermission.employee_role_id == employee_role_id)
        & (BusinessEmployeeRolePermission.business_id == business.id)
    )
    existing_ids = {perm.permission_id for perm in base_query}

    delete_ids = existing_ids - permission_ids
    new_ids = permission_ids - existing_ids

    if delete_ids:
        BusinessEmployeeRolePermission.delete().where(
            (BusinessEmployeeRolePermission.node_id == node.id)
            & (BusinessEmployeeRolePermission.employee_role_id == employee_role_id)
            & (BusinessEmployeeRolePermission.business_id == business.id)
            & (BusinessEmployeeRolePermission.permission_id.in_(delete_ids))
        ).execute()

    if new_ids:
        BusinessEmployeeRolePermission.insert_many(
            [
                {
                    "node_id": node.id,
                    "employee_role_id": employee_role_id,
                    "permission_id": pid,
                    "business_id": business.id,
                }
                for pid in new_ids
            ]
        ).execute()

    return SuccessResponse(message=ConstantMessages.PERMISSION_UPDATED)


@router.post(
    "/nodes/bulk_update",
    summary="Update Node Permissions",
    description="Update the permissions for a bulk nodes and employee role, ensuring the nodes and role exist.",
    response_model=SuccessResponse,
    dependencies=[
        Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.get_current_auth_token),
        Depends(WildcardAuthHelper.employee_is_super_admin)
    ],
)
async def bulk_update_nodes(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(
        ...,
        example={
            "employee_role_id": 2,
            "permissions": {1: {1, 2, 3, 4}},
        },
    ),
):
    """
    Update the permissions for a given nodes and employee role.

    This endpoint updates the permissions associated with a specific node and
    employee role. It checks if the node exists and belongs to type "EmployeeNode",
    and if the employee role is valid. It then updates the permissions by adding
    new ones and removing the ones no longer needed.

    Args:
        body (dict): A dictionary containing the `employee_role_id` and a list of
                     `permissions`.

    Returns:
        SuccessResponse: A response object indicating the update was successful.
    """
    try:
        employee_role_id = body.get("employee_role_id")
        if not employee_role_id:
            raise CustomValidationError(error=ConstantMessages.EMPLOYEE_ROLE_REQUIRED)

        employee_role = EmployeeRole.get_or_none(EmployeeRole.id == employee_role_id)
        if not employee_role:
            raise CustomValidationError(error=ConstantMessages.INVALID_EMPLOYEE_ROLE)

        node_permissions = body.get("permissions")
        node_hash = {}
        node_ids = set()

        for node_id, permission_ids in node_permissions.items():
            node_id = int(node_id)
            permission_ids = set(permission_ids)
            node_hash[node_id] = permission_ids
            node_ids.add(node_id)

        # Retrieve existing permissions for all nodes in one query
        existing_permissions = BusinessEmployeeRolePermission.select().where(
            (BusinessEmployeeRolePermission.node_id.in_(node_ids))
            & (BusinessEmployeeRolePermission.business_id == business.id)
            & (BusinessEmployeeRolePermission.employee_role_id == employee_role.id)
        )

        # Create a dictionary to quickly lookup existing permissions by node_id
        existing_permissions_dict = {
            node_id: set(
                perm.permission_id
                for perm in existing_permissions
                if perm.node_id == node_id
            )
            for node_id in node_ids
        }

        # Prepare for bulk delete and insert operations
        bulk_deletes = []
        bulk_inserts = []

        for node_id, permission_ids in node_hash.items():
            existing_ids = existing_permissions_dict.get(node_id, set())

            delete_ids = existing_ids - permission_ids
            new_ids = permission_ids - existing_ids

            if delete_ids:
                bulk_deletes.append({"node_id": node_id, "delete_ids": delete_ids})

            if new_ids:
                bulk_inserts.extend(
                    [
                        {
                            "node_id": node_id,
                            "permission_id": pid,
                            "business_id": business.id,
                            "employee_role_id": employee_role_id,
                        }
                        for pid in new_ids
                    ]
                )

        # Perform bulk deletions
        for delete in bulk_deletes:
            BusinessEmployeeRolePermission.delete().where(
                (BusinessEmployeeRolePermission.node_id == delete["node_id"])
                & (BusinessEmployeeRolePermission.employee_role_id == employee_role_id)
                & (BusinessEmployeeRolePermission.business_id == business.id)
                & (
                    BusinessEmployeeRolePermission.permission_id.in_(
                        delete["delete_ids"]
                    )
                )
            ).execute()

        # Perform bulk insertions
        if bulk_inserts:
            BusinessEmployeeRolePermission.insert_many(bulk_inserts).execute()

        return SuccessResponse(message=ConstantMessages.PERMISSION_UPDATED)
    except CustomValidationError as e:
        raise e
    except Exception as e:
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/nodes/detail",
    summary="WildCard Nodes list",
    description="Retrieve a comprehensive list of nodes for wildcard subdomains.",
    response_model=SuccessResponse,
)
async def nodes(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(..., example={"unique_key": "employee_dashboard"}),
):
    """
    Retrieve a list of all nodes available in the system.
    """

    unique_key = body.get("unique_key")

    node = Node.get_or_none(Node.unique_key == unique_key, Node.type == "EmployeeNode")
    if not node:
        raise CustomValidationError(error=ConstantMessages.NODE_REQUIRED)

    permission_roles = BusinessEmployeeRolePermission.select().where(
        (BusinessEmployeeRolePermission.node_id == node.id)
        & (BusinessEmployeeRolePermission.business_id == business.id)
    )

    roles = []
    permissions_dict = {}

    for permission_role in permission_roles:
        role_name = permission_role.employee_role.name
        if role_name not in roles:
            roles.append(role_name)
        if role_name not in permissions_dict:
            permissions_dict[role_name] = []
        permissions_dict[role_name].append(permission_role.permission.name)

    return SuccessResponse(
        message="Data updated successfully",
        data={
            "roles": roles,
            "roles_permission": permissions_dict,
            "title": node.name,
            "singular_name": node.singular_name,
            "description": node.description,
        },
    )


@router.post(
    "/permissions/selected_page",
    summary="WildCard Selected Page Permission",
    description="Retrieve a comprehensive list of nodes for wildcard subdomains.",
    response_model=SuccessResponse,
    dependencies=[
        Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.get_current_auth_token),
    ],
)
async def selected_page_permissions(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    body: dict = Body(...),
):
    """
    Retrieve a list of all nodes available in the system.
    """
    try:
        unique_key = body.get("unique_key")

        node = Node.get_or_none(
            Node.unique_key == unique_key, Node.type == "EmployeeNode"
        )
        if not node:
            raise CustomValidationError(error=ConstantMessages.NODE_REQUIRED)

        if current_employee.employee_role.name == "Super Admin":
            permissions = Permission.pluck("name")
        else:
            permission_roles = BusinessEmployeeRolePermission.select().where(
                (BusinessEmployeeRolePermission.node_id == node.id)
                & (BusinessEmployeeRolePermission.business_id == business.id)
                & (
                    BusinessEmployeeRolePermission.employee_role_id
                    == current_employee.employee_role_id
                )
            )
            permissions = [role.permission.name for role in permission_roles]

        return SuccessResponse(message="Data updated successfully", data=permissions)
    except Exception as e:
        logging.error(f"Permisions Page {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)
