from fastapi import APIRouter, Depends, Query, HTTPException, status, Body, Request
from app.schema import PaginationResponse, SuccessResponse
from app.models import Business, BusinessJobRequest
from app.exceptions import RecordNotFoundException
from app.helper import Wildcard<PERSON><PERSON>Helper
from app.constants import <PERSON><PERSON>Name, PermissionName
from peewee import fn
from typing import Optional
import logging

router = APIRouter(
    prefix="/job_requests",
    tags=["Wildcard JobRequest API"],
    dependencies=[Depends(WildcardAuthHelper.get_current_employee),
        Depends(WildcardAuthHelper.get_current_auth_token),
    ],
)


# Helper function to get job_request by ID
async def get_job_request(
    id: int, business: Business = Depends(WildcardAuthHelper.validate_subdomain)
) -> BusinessJobRequest:
    """
    Retrieve a job_request by its ID.

    Args:
        id (int): The ID of the job request to retrieve.
        business (Business): The current business, provided by dependency injection.

    Returns:
        JobRequest: The job request object if found.

    Raises:
        RecordNotFoundException: If the job request does not exist.
    """
    job_request = BusinessJobRequest.get_or_none(id=id, business_id=business.id)
    if not job_request:
        raise RecordNotFoundException(message="JobRequest does not exist")
    return job_request


# ------------------------------ router functions ------------------------------


@router.get(
    "",
    summary="List of Categories",
    description="Retrieve a paginated list of job requests.",
    response_model=PaginationResponse,
)
def get_job_requests(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    search: Optional[str] = Query(
        None, description="Search term to filter job requests"
    ),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve a paginated list of job requests.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of job requests per page. Defaults to 10.
        search (Optional[str]): Search term to filter job requests.

    Returns:
        PaginationResponse: Paginated list of job requests.
    """

    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_JOB_REQUESTS,
        perm_names=PermissionName.READ_ONLY,
    )

    try:
        base_query = BusinessJobRequest.select().where(
            BusinessJobRequest.business_id == business.id
        )

        if search:
            search = search.strip().lower()
            base_query = base_query.where(
                fn.lower(BusinessJobRequest.name).contains(search)
            )

        total_records = base_query.count()

        offset = (page - 1) * limit
        records = (
            base_query.offset(offset)
            .limit(limit)
            .order_by(BusinessJobRequest.id.desc())
        )
        # Prepare job request list
        rows = [record.info() for record in records]

        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            message="Data fetched successfully",
        )
    except Exception as e:
        logging.error(f"Exception in job request list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.get(
    "/{id}",
    summary="Get JobRequest",
    description="Retrieve job request details.",
    response_model=SuccessResponse,
)
async def get_job_request_detail(
    request: Request,
    job_request: BusinessJobRequest = Depends(get_job_request),
):
    """
    Endpoint to retrieve job request details.

    Args:
        job request (JobRequest): The job request instance, provided by dependency injection.

    Returns:
        SuccessResponse: Success message along with job request details.
    """

    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_JOB_REQUESTS,
        perm_names=PermissionName.READ_ONLY,
    )

    try:
        # Return success response
        return SuccessResponse(
            message="JobRequest details fetched successfully.", data=job_request.info()
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.put(
    "/{id}/status",
    summary="Update JobRequest Status",
    description="Update the status of a job request.",
    response_model=SuccessResponse,
)
async def update_job_request_status(
    request: Request,
    job_request: BusinessJobRequest = Depends(get_job_request),
    body: dict = Body(..., example={"status": 1}),
):
    """
    Update the status of a job request.

    Args:
        id (int): The ID of the job request.
        job request (JobRequest): The job request instance, provided by dependency injection.
        body (dict): The request body containing the new status.

    Returns:
        SuccessResponse: The response containing the result message and updated job request information.
    """

    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_JOB_REQUESTS,
        perm_names=PermissionName.READ_ONLY,
    )

    try:
        job_request_status = body.get("status")
        job_request.status = job_request_status
        job_request.save()

        return SuccessResponse(
            message="Status updated successfully.", data=job_request.info()
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
